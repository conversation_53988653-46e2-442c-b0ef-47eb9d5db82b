<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>Lead Manager — Offline Single File</title>
<style>
  :root{
    --bg:#f7f7fb; --ink:#0f172a; --muted:#6b7280; --card:#ffffff; --pri:#2563eb;
    --ok:#10b981; --warn:#f59e0b; --err:#ef4444; --bd:#e5e7eb;
  }
  *{box-sizing:border-box}
  html,body{height:100%}
  body{margin:0;background:var(--bg);color:var(--ink);font-family:ui-sans-serif,system-ui,Segoe UI,Roboto,Arial,sans-serif}
  .app{display:flex;flex-direction:column;height:100%}
  header{
    position:sticky;top:0;z-index:10;
    display:flex;gap:.75rem;align-items:center;justify-content:space-between;
    padding:.8rem 1rem;border-bottom:1px solid var(--bd);background:rgba(255,255,255,.75);backdrop-filter:blur(6px)
  }
  header .title{font-weight:700;letter-spacing:.3px}
  header .title small{display:block;font-weight:500;color:var(--muted);font-size:.8rem}
  .controls{display:flex;gap:.5rem;flex-wrap:wrap;align-items:center}
  .controls input[type="search"], .controls select{
    padding:.6rem .7rem;border:1px solid var(--bd);border-radius:.6rem;background:#fff;min-width:220px;outline:0
  }
  .btn{appearance:none;border:0;background:var(--pri);color:#fff;padding:.65rem .9rem;border-radius:.8rem;font-weight:600;cursor:pointer}
  .btn.secondary{background:#fff;color:var(--ink);border:1px solid var(--bd)}
  .btn.ghost{background:transparent;color:var(--pri);border:1px dashed var(--pri)}
  .btn.small{padding:.45rem .6rem;border-radius:.6rem;font-weight:600}
  .btn:disabled{opacity:.45;cursor:not-allowed}
  .toolbar{display:flex;gap:.5rem;align-items:center}
  main{flex:1;min-height:0;overflow:auto;padding:1rem}
  .board{display:grid;grid-auto-flow:column;grid-auto-columns:minmax(280px,1fr);gap:1rem;min-height:100%}
  .column{display:flex;flex-direction:column;background:var(--card);border:1px solid var(--bd);border-radius:1rem;min-height:60vh}
  .column header{position:relative;top:auto;background:transparent;border:0;backdrop-filter:none;padding:.8rem 1rem}
  .column header h3{margin:0;font-size:1rem}
  .stage-badge{display:inline-flex;align-items:center;gap:.35rem;padding:.2rem .5rem;border-radius:999px;border:1px solid var(--bd);background:#fff;font-size:.75rem;color:var(--muted);}
  .count{font-variant-numeric:tabular-nums;color:var(--muted);font-weight:600;margin-left:.35rem}
  .dropzone{flex:1;padding:0 .8rem .8rem;overflow:auto}
  .dropzone.dragover{outline:2px dashed var(--pri);outline-offset:-6px;border-radius:.8rem}
  .card{user-select:none;background:#fff;border:1px solid var(--bd);border-radius:.9rem;padding:.75rem;margin:.8rem 0;box-shadow:0 2px 8px rgba(15,23,42,.05)}
  .card .name{font-weight:700;margin-bottom:.35rem}
  .meta{display:flex;flex-wrap:wrap;gap:.35rem;margin:.25rem 0 .4rem}
  .chip{display:inline-flex;align-items:center;gap:.35rem;padding:.2rem .5rem;border-radius:999px;background:#f3f4f6;color:#111827;border:1px solid #e5e7eb;font-size:.75rem}
  .chip .dot{width:.5rem;height:.5rem;border-radius:999px;background:var(--pri)}
  .svgs{display:flex;gap:.35rem;margin-top:.25rem}
  .svgs a{display:inline-flex;align-items:center;gap:.25rem;font-size:.8rem;color:var(--pri);text-decoration:none;border:1px dashed transparent;padding:.15rem .35rem;border-radius:.4rem}
  .svgs a:focus, .svgs a:hover{border-color:var(--pri);background:#eff6ff}
  .actions{display:flex;gap:.35rem;margin-top:.5rem}
  .empty{border:2px dashed var(--bd);border-radius:.8rem;padding:1rem;text-align:center;color:var(--muted);margin:.8rem 0}
  /* Modal */
  dialog{border:0;border-radius:1rem;padding:0;max-width:780px;width:96%;box-shadow:0 12px 40px rgba(0,0,0,.2)}
  .modal-header{display:flex;align-items:center;justify-content:space-between;padding:1rem 1.1rem;border-bottom:1px solid var(--bd)}
  .modal-body{padding:1rem 1.1rem}
  .form{display:grid;grid-template-columns:repeat(12,1fr);gap:.75rem}
  .form label{font-size:.86rem;color:var(--muted)}
  .form input,.form select,.form textarea{width:100%;padding:.55rem .6rem;border:1px solid var(--bd);border-radius:.6rem;font-size:.95rem}
  .form textarea{min-height:84px;resize:vertical}
  .col-6{grid-column:span 6}
  .col-4{grid-column:span 4}
  .col-8{grid-column:span 8}
  .col-12{grid-column:span 12}
  .services{display:flex;flex-wrap:wrap;gap:.5rem}
  .service-tag{display:inline-flex;gap:.35rem;align-items:center;border:1px solid var(--bd);padding:.35rem .6rem;border-radius:.6rem;background:#fff;cursor:pointer}
  .service-tag input{accent-color:var(--pri)}
  .modal-footer{display:flex;justify-content:space-between;gap:.5rem;padding:.9rem 1.1rem;border-top:1px solid var(--bd)}
  .hint{color:var(--muted);font-size:.85rem}
  @media (max-width:900px){ .col-6,.col-4,.col-8{grid-column:span 12} }
</style>
</head>
<body>
<div class="app">
  <header>
    <div class="title">
      Lead Manager
      <small>Pipeline stages: New → Contacted → Quote Sent → Follow-Up → Booked → Lost</small>
    </div>
    <div class="toolbar">
      <div class="controls">
        <input id="search" type="search" placeholder="Search leads (name, phone, city, referrer, instagram)">
        <select id="filterOccasion">
          <option value="">All occasions</option>
        </select>
        <select id="filterService">
          <option value="">All services</option>
        </select>
      </div>
      <button class="btn" id="addLeadBtn">+ Add lead</button>
      <button class="btn secondary" id="exportBtn">Export JSON</button>
      <label class="btn ghost" for="importFile">Import JSON</label>
      <input id="importFile" type="file" accept="application/json" style="display:none">
    </div>
  </header>

  <main>
    <div id="board" class="board" aria-live="polite"></div>
  </main>
</div>

<!-- Lead Modal -->
<dialog id="leadModal">
  <form method="dialog" id="leadForm">
    <div class="modal-header">
      <strong id="modalTitle">New lead</strong>
      <button class="btn small secondary" type="button" id="closeModal">Close</button>
    </div>
    <div class="modal-body">
      <div class="form">
        <div class="col-6">
          <label>Name</label>
          <input id="name" required placeholder="Full name">
        </div>
        <div class="col-6">
          <label>Mobile</label>
          <input id="mobile" placeholder="+32 468 24 11 26">
        </div>
        <div class="col-6">
          <label>Instagram</label>
          <input id="instagram" placeholder="@username or full link">
        </div>
        <div class="col-6">
          <label>Referrer</label>
          <input id="referrer" placeholder="Who referred them?">
        </div>
        <div class="col-4">
          <label>City</label>
          <input id="city" placeholder="Ghent">
        </div>
        <div class="col-4">
          <label>Country</label>
          <input id="country" placeholder="Belgium">
        </div>
        <div class="col-4">
          <label>Occasion</label>
          <select id="occasion"></select>
        </div>
        <div class="col-12">
          <label>Requested services</label>
          <div id="servicesWrap" class="services"></div>
        </div>
        <div class="col-12">
          <label>Notes / Next step</label>
          <textarea id="notes" placeholder="e.g., send price list; follow up on Friday"></textarea>
        </div>
        <div class="col-12">
          <span class="hint">Tip: Use drag & drop on the board to move a lead through stages.</span>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <div><span id="metaInfo" class="hint"></span></div>
      <div style="display:flex;gap:.5rem">
        <button class="btn secondary" value="cancel" type="button" id="cancelBtn">Cancel</button>
        <button class="btn" value="default" id="saveBtn">Save lead</button>
      </div>
    </div>
  </form>
</dialog>

<script>
(() => {
  // UPDATED: "Qualified" and "Needs Assessed" removed
  const STAGES = ['New','Contacted','Quote Sent','Follow-Up','Booked','Lost'];

  const OCCASIONS = ['Wedding','Engagement','Birthday','Corporate Event','Family','Portrait','Boudoir','Maternity','Newborn','Other'];
  const SERVICES = [
    'Wedding Photography','Wedding Videography','Engagement Photography','Event Videography','Portrait Session',
    'Studio Session','Boudoir','Maternity Session','Newborn Session','Corporate/Promo','Product/Branding'
  ];

  const qs = (s, el=document) => el.querySelector(s);
  const qsa = (s, el=document) => [...el.querySelectorAll(s)];
  const elBoard = qs('#board');
  const elSearch = qs('#search');
  const elFilterOccasion = qs('#filterOccasion');
  const elFilterService = qs('#filterService');
  const elAddLeadBtn = qs('#addLeadBtn');
  const elExportBtn = qs('#exportBtn');
  const elImportFile = qs('#importFile');

  const modal = qs('#leadModal');
  const form = qs('#leadForm');
  const fields = {
    name: qs('#name'), mobile: qs('#mobile'), instagram: qs('#instagram'),
    city: qs('#city'), country: qs('#country'), referrer: qs('#referrer'),
    occasion: qs('#occasion'), notes: qs('#notes')
  };
  const servicesWrap = qs('#servicesWrap');
  const modalTitle = qs('#modalTitle');
  const metaInfo = qs('#metaInfo');

  let leads = load();
  // OPTIONAL: migrate old data if it had the removed stages
  migrateRemovedStages();
  let editId = null;
  let filters = { text:'', occasion:'', service:'' };

  // Populate filters/options
  function initOptions(){
    OCCASIONS.forEach(o => {
      let opt = document.createElement('option'); opt.value = o; opt.textContent = o;
      fields.occasion.appendChild(opt.cloneNode(true));
      elFilterOccasion.appendChild(opt);
    });
    SERVICES.forEach(s => {
      const tag = document.createElement('label');
      tag.className = 'service-tag';
      const cb = document.createElement('input');
      cb.type='checkbox'; cb.value=s; cb.name='services';
      const span = document.createElement('span'); span.textContent = s;
      tag.append(cb, span);
      servicesWrap.appendChild(tag);

      let opt = document.createElement('option'); opt.value = s; opt.textContent = s;
      elFilterService.appendChild(opt);
    });
  }

  function save(){
    localStorage.setItem('lead_manager_v1', JSON.stringify(leads));
  }
  function load(){
    try{
      return JSON.parse(localStorage.getItem('lead_manager_v1')) || [];
    }catch(e){ return []; }
  }

  function uid(){
    return 'L' + Math.random().toString(36).slice(2,9) + Date.now().toString(36).slice(-4);
  }

  function nowISO(){ return new Date().toISOString(); }

  function applyFilters(list){
    return list.filter(l => {
      const txt = (l.name+' '+(l.mobile||'')+' '+(l.city||'')+' '+(l.referrer||'')+' '+(l.instagram||'')).toLowerCase();
      if(filters.text && !txt.includes(filters.text.toLowerCase())) return false;
      if(filters.occasion && l.occasion !== filters.occasion) return false;
      if(filters.service && !(l.services||[]).includes(filters.service)) return false;
      return true;
    });
  }

  function render(){
    elBoard.innerHTML = '';
    STAGES.forEach(stage => {
      const col = document.createElement('section');
      col.className='column';
      col.dataset.stage=stage;

      const head = document.createElement('header');
      const h = document.createElement('h3');
      h.innerHTML = `<span class="stage-badge"><span class="dot" style="background:${stageColor(stage)}"></span>${stage}</span>`;
      const c = document.createElement('span'); c.className='count';
      const count = applyFilters(leads.filter(l => l.stage===stage)).length;
      c.textContent = '· ' + count;
      head.append(h,c);

      const dz = document.createElement('div');
      dz.className='dropzone'; dz.dataset.stage=stage;
      dz.addEventListener('dragover', e => { e.preventDefault(); dz.classList.add('dragover'); });
      dz.addEventListener('dragleave', () => dz.classList.remove('dragover'));
      dz.addEventListener('drop', e => {
        e.preventDefault(); dz.classList.remove('dragover');
        const id = e.dataTransfer.getData('text/id');
        moveLead(id, stage);
      });

      const items = applyFilters(leads.filter(l => l.stage===stage));
      if(items.length===0){
        const empty = document.createElement('div'); empty.className='empty'; empty.textContent='Drop leads here';
        dz.appendChild(empty);
      }else{
        items.sort((a,b)=> new Date(b.updatedAt||b.createdAt) - new Date(a.updatedAt||a.createdAt))
             .forEach(lead => dz.appendChild(card(lead)));
      }

      col.append(head,dz);
      elBoard.appendChild(col);
    });
  }

  function card(lead){
    const el = document.createElement('article'); el.className='card'; el.draggable=true;
    el.addEventListener('dragstart', e => {
      e.dataTransfer.setData('text/id', lead.id);
      e.dataTransfer.effectAllowed='move';
    });
    const name = document.createElement('div'); name.className='name'; name.textContent = lead.name;
    const meta = document.createElement('div'); meta.className='meta';
    if(lead.city) meta.append(chip('📍', lead.city + (lead.country ? ', '+lead.country : '')));
    if(lead.mobile) meta.append(chip('☎️', lead.mobile));
    if(lead.referrer) meta.append(chip('🧩', 'Ref: '+lead.referrer));
    if(lead.occasion) meta.append(chip('🎯', lead.occasion));
    (lead.services||[]).slice(0,3).forEach(s => meta.append(chip('🛠️', s)));
    if((lead.services||[]).length>3) meta.append(chip('➕', `${(lead.services||[]).length-3} more`));

    const links = document.createElement('div'); links.className='svgs';
    if(lead.mobile){
      const aTel = link(`tel:${cleanPhone(lead.mobile)}`, 'Call');
      links.append(aTel);
      const aWa = link(`https://wa.me/${cleanPhone(lead.mobile)}`, 'WhatsApp');
      links.append(aWa);
    }
    if(lead.instagram){
      const url = toInstaUrl(lead.instagram);
      const aIG = link(url, 'Instagram');
      links.append(aIG);
    }

    const actions = document.createElement('div'); actions.className='actions';
    const editBtn = document.createElement('button'); editBtn.className='btn small secondary'; editBtn.textContent='Edit';
    editBtn.addEventListener('click', () => openModal(lead.id));
    const delBtn = document.createElement('button'); delBtn.className='btn small ghost'; delBtn.textContent='Delete';
    delBtn.addEventListener('click', () => {
      if(confirm('Delete this lead?')){
        leads = leads.filter(l => l.id!==lead.id); save(); render();
      }
    });
    actions.append(editBtn, delBtn);

    el.append(name, meta, links, actions);
    return el;
  }

  function chip(icon, text){
    const c = document.createElement('span'); c.className='chip';
    const i = document.createElement('span'); i.textContent = icon;
    const t = document.createElement('span'); t.textContent = text;
    c.append(i,t); return c;
  }

  function link(href, text){
    const a = document.createElement('a'); a.href=href; a.target='_blank'; a.rel='noopener'; a.textContent=text;
    return a;
  }

  function cleanPhone(p){ return (p||'').replace(/[^\d+]/g,'').replace(/^00/,'+'); }

  function toInstaUrl(v){
    v = (v||'').trim();
    if(!v) return '#';
    if(v.startsWith('http')) return v;
    if(v.startsWith('@')) v = v.slice(1);
    return 'https://instagram.com/' + v;
  }

  function moveLead(id, toStage){
    const i = leads.findIndex(l=>l.id===id);
    if(i===-1) return;
    leads[i].stage = toStage;
    leads[i].updatedAt = nowISO();
    save(); render();
  }

  function openModal(id=null){
    editId = id;
    if(id){
      modalTitle.textContent = 'Edit lead';
      const l = leads.find(x=>x.id===id);
      fields.name.value = l.name||'';
      fields.mobile.value = l.mobile||'';
      fields.instagram.value = l.instagram||'';
      fields.city.value = l.city||'';
      fields.country.value = l.country||'';
      fields.referrer.value = l.referrer||'';
      fields.occasion.value = l.occasion||'';
      fields.notes.value = l.notes||'';
      qsa('input[name="services"]', servicesWrap).forEach(cb => cb.checked = (l.services||[]).includes(cb.value));
      metaInfo.textContent = `Stage: ${l.stage} · Created ${fmtDate(l.createdAt)} · Updated ${fmtDate(l.updatedAt||l.createdAt)}`;
    }else{
      modalTitle.textContent = 'New lead';
      form.reset();
      qsa('input[name="services"]', servicesWrap).forEach(cb => cb.checked=false);
      fields.occasion.value='';
      metaInfo.textContent = '';
    }
    if(typeof modal.showModal === 'function') modal.showModal(); else alert('Your browser does not support <dialog>. Use a modern browser.');
  }

  function closeModal(){ modal.close(); }

  function getFormData(){
    const svcs = qsa('input[name="services"]:checked', servicesWrap).map(i=>i.value);
    return {
      name: fields.name.value.trim(),
      mobile: fields.mobile.value.trim(),
      instagram: fields.instagram.value.trim(),
      city: fields.city.value.trim(),
      country: fields.country.value.trim(),
      referrer: fields.referrer.value.trim(),
      occasion: fields.occasion.value,
      services: svcs,
      notes: fields.notes.value.trim()
    };
  }

  function fmtDate(iso){
    if(!iso) return '—';
    const d = new Date(iso);
    return d.toLocaleString();
  }

  // Events
  elAddLeadBtn.addEventListener('click', () => openModal());
  qs('#closeModal').addEventListener('click', closeModal);
  qs('#cancelBtn').addEventListener('click', closeModal);

  form.addEventListener('submit', (e) => {
    e.preventDefault();
    const data = getFormData();
    if(!data.name){ alert('Name is required'); return; }

    if(editId){
      const i = leads.findIndex(l=>l.id===editId);
      if(i>-1){
        leads[i] = {...leads[i], ...data, updatedAt: nowISO()};
      }
    }else{
      leads.push({ id: uid(), stage: STAGES[0], createdAt: nowISO(), updatedAt: nowISO(), ...data });
    }
    save(); render(); closeModal();
  });

  elExportBtn.addEventListener('click', () => {
    const filename = prompt('File name for export (e.g., leads_2025-08-14.json):','');
    if(filename===null) return;
    const name = filename.trim() || 'leads.json';
    const blob = new Blob([JSON.stringify(leads, null, 2)], {type:'application/json'});
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url; a.download = name; document.body.appendChild(a); a.click();
    setTimeout(()=>{ URL.revokeObjectURL(url); a.remove(); }, 500);
  });

  elImportFile.addEventListener('change', async (e) => {
    const file = e.target.files[0];
    if(!file) return;
    try{
      const text = await file.text();
      const data = JSON.parse(text);
      if(!Array.isArray(data)) throw new Error('Invalid file format');
      if(!confirm('Import will replace current leads. Continue?')) return;
      leads = data; migrateRemovedStages(); save(); render();
    }catch(err){
      alert('Import failed: ' + err.message);
    }finally{
      e.target.value='';
    }
  });

  elSearch.addEventListener('input', () => { filters.text = elSearch.value; render(); });
  elFilterOccasion.addEventListener('change', () => { filters.occasion = elFilterOccasion.value; render(); });
  elFilterService.addEventListener('change', () => { filters.service = elFilterService.value; render(); });

  function stageColor(stage){
    const map = {
      'New':'#2563eb',
      'Contacted':'#0ea5e9',
      'Quote Sent':'#f59e0b',
      'Follow-Up':'#f97316',
      'Booked':'#22c55e',
      'Lost':'#ef4444'
    };
    return map[stage] || 'var(--pri)';
  }

  // If old data had "Qualified" or "Needs Assessed", map them to "Contacted"
  function migrateRemovedStages(){
    let changed = false;
    leads.forEach(l => {
      if(l.stage === 'Qualified' || l.stage === 'Needs Assessed'){
        l.stage = 'Contacted'; l.updatedAt = nowISO(); changed = true;
      }
    });
    if(changed) save();
  }

  // Initialize
  initOptions();
  render();
})();
</script>
</body>
</html>
