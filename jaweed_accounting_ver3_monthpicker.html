<!DOCTYPE html>
<html lang="fa" dir="ltr">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Simple Offline Accounting</title>
  <style>
    :root{
      --bg:#f8fafc; --card:#ffffff; --ink:#0f172a; --muted:#64748b; --bd:#e2e8f0;
      --pri:#2563eb; --ok:#10b981; --err:#ef4444; --vio:#7c3aed;
    }
    *{box-sizing:border-box}
    body{margin:0; font-family: system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial;
      background:var(--bg); color:var(--ink);}
    .container{max-width:1260px; margin:32px auto; padding:0 16px;}
    header{display:flex; flex-wrap:wrap; align-items:center; gap:12px; justify-content:space-between; margin-bottom:12px;}
    h1{margin:0; font-size:clamp(20px,3.6vw,28px)}
    .badge{font-size:12px; color:var(--muted)}
    .row{display:flex; gap:12px; flex-wrap:wrap;}
    .col{flex:1 1 220px;}
    .card{background:var(--card); border:1px solid var(--bd); border-radius:16px;}
    .pad{padding:16px}
    label{display:block; font-size:12px; color:var(--muted); margin-bottom:6px;}
    input, select, button{width:100%; padding:10px 12px; border:1px solid var(--bd); border-radius:12px; background:#fff; color:var(--ink); font:inherit; outline:none;}
    input:focus, select:focus{border-color:var(--pri); box-shadow:0 0 0 3px rgba(37,99,235,.15);}
    input[readonly]{background:#f8fafc}
    button{width:auto; cursor:pointer; border:1px solid var(--pri); background:var(--pri); color:#fff; font-weight:600;}
    button.minor{background:#fff; color:var(--ink); border-color:var(--bd);}
    button.danger{background:#ef4444; border-color:#ef4444; color:#fff;}
    .summary{display:grid; grid-template-columns:repeat(4,1fr); gap:12px;}
    .kpi{padding:14px 16px; border-radius:14px; border:1px dashed var(--bd); background:#fff;}
    .kpi h3{margin:0 0 6px; font-size:12px; color:var(--muted); font-weight:500}
    .kpi div{font-size:18px; font-weight:700}
    .kpi.income div{color:var(--ok)} .kpi.expense div{color:var(--err)} .kpi.vat div{color:var(--vio)} .kpi.balance div{color:var(--ink)}
    table{width:100%; border-collapse:separate; border-spacing:0 8px;}
    thead th{font-size:12px; color:var(--muted); text-align:left; padding:0 10px;}
    tbody td{background:#fff; border:1px solid var(--bd); padding:10px; vertical-align:top;}
    tbody tr td:first-child{border-top-left-radius:12px; border-bottom-left-radius:12px;}
    tbody tr td:last-child{border-top-right-radius:12px; border-bottom-right-radius:12px;}
    .right{text-align:right}
    .nowrap{white-space:nowrap}
    .tag{display:inline-block; padding:2px 8px; border-radius:999px; font-size:12px; border:1px solid var(--bd); background:#fff;}
    .tag.income{border-color:#bbf7d0; background:#ecfdf5; color:#065f46;}
    .tag.expense{border-color:#fecaca; background:#fef2f2; color:#7f1d1d;}
    .actions{display:flex; gap:6px; justify-content:flex-end;}
    .sep{height:1px; background:var(--bd); margin:12px 0;}
    .mono{font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Courier New", monospace;}
    .chart-wrap{position:relative; width:100%; height:360px;}
    canvas{display:block; width:100%; height:100%;}
    .legend{display:flex; gap:8px; align-items:center; flex-wrap:wrap; font-size:12px; color:var(--muted);}
    .legend .dot{width:10px; height:10px; border-radius:2px; display:inline-block; background:#2563eb;}
    .tabs{display:flex; gap:8px; flex-wrap:wrap; margin:12px 0;}
    .tabs button{background:#fff; color:var(--ink); border:1px solid var(--bd); padding:8px 12px; border-radius:999px; font-weight:600;}
    .tabs button[aria-selected="true"]{background:var(--pri); color:#fff; border-color:var(--pri);}
    .tab{display:none} .tab.active{display:block}
    @media (max-width:920px){ .summary{grid-template-columns:1fr 1fr} .chart-wrap{height:320px} }
    @media (max-width:540px){ .summary{grid-template-columns:1fr} .chart-wrap{height:280px} }
  </style>
</head>
<body>
<div class="container">
  <header>
    <div>
      <h1>Simple Offline Accounting</h1>
      <div class="badge">Runs locally · No internet/database</div>
    </div>
    <div class="row" style="gap:8px; flex-wrap:wrap;">
      <button id="exportJsonBtn">Export JSON</button>
      <button id="importJsonBtn" class="minor">Import JSON</button>
      <input id="importFile" type="file" accept="application/json" style="display:none" />
      <button id="exportCsvBtn" class="minor">Export CSV</button>
      <button id="clearBtn" class="danger">Clear All</button>
    </div>
  </header>

  <div class="card pad">
    <div class="row" style="align-items:flex-end;">
      <div class="col" style="max-width:260px;">
        <label for="month">Filter: Month</label>
        <input id="month" type="month">
      </div>
      <div class="col">
        <label for="search">Filter: Search</label>
        <input id="search" placeholder="Find in category or description">
      </div>
    </div>
  </div>

  <nav class="tabs" role="tablist">
    <button class="tab-btn" data-tab="dashboard" aria-selected="true">Dashboard</button>
    <button class="tab-btn" data-tab="add">Add Entry</button>
    <button class="tab-btn" data-tab="entries">Entries</button>
    <button class="tab-btn" data-tab="charts">Charts</button>
    <button class="tab-btn" data-tab="customers">Customers</button>
    <button class="tab-btn" data-tab="categories">Categories</button>
    <button class="tab-btn" data-tab="report">Monthly Report</button>
    <button class="tab-btn" data-tab="daily">Daily Report</button>
    <button class="tab-btn" data-tab="topcustomers">Top Customers</button>
  </nav>

  <section id="tab-dashboard" class="tab active">
    <div class="summary">
      <div class="kpi income"><h3>Total Income (Net)</h3><div id="sumIncome" class="mono">€0.00</div></div>
      <div class="kpi vat"><h3>VAT Collected (Income)</h3><div id="sumVat" class="mono">€0.00</div></div>
      <div class="kpi expense"><h3>Total Expenses</h3><div id="sumExpense" class="mono">€0.00</div></div>
      <div class="kpi balance"><h3>Balance (Net - Expenses)</h3><div id="sumBalance" class="mono">€0.00</div></div>
    </div>
  </section>

  <section id="tab-add" class="tab">
    <div class="card pad">
      <div class="row">
        <div class="col">
          <label for="date">Date</label>
          <input id="date" type="date" />
        </div>
        <div class="col">
          <label for="type">Type</label>
          <select id="type">
            <option value="income">Income</option>
            <option value="expense">Expense</option>
          </select>
        </div>
        <div class="col">
          <label for="category">Category</label>
          <div class="row" style="gap:8px;">
            <input id="category" list="catlist" placeholder="e.g., Sales, Rent, Equipment" />
            <button id="quickAddCategory" class="minor">New</button>
          </div>
          <datalist id="catlist"></datalist>
        </div>
        <div class="col">
          <label for="description">Description</label>
          <input id="description" placeholder="Optional note" />
        </div>
        <div class="col">
          <label for="amount_income">Income Amount (Gross)</label>
          <input id="amount_income" type="number" step="0.01" inputmode="decimal" placeholder="0.00" />
        </div>
        <div class="col">
          <label for="amount_expense">Expense Amount</label>
          <input id="amount_expense" type="number" step="0.01" inputmode="decimal" placeholder="0.00" />
        </div>
      </div>
      <div class="row" style="margin-top:12px; align-items:flex-end;">
        <div class="col" id="customerCol" style="display:none;">
          <label for="customer">Customer (for income)</label>
          <div class="row" style="gap:8px;">
            <select id="customer"></select>
            <button id="quickAddCustomer" class="minor">New</button>
          </div>
        </div>
        <div class="col" id="vatCol" style="display:none;">
          <label for="vatRate">VAT (%)</label>
          <div class="row" style="gap:8px; align-items:center;">
            <input id="vatRate" type="number" step="0.01" value="21" disabled />
            <span class="badge">Locked at 21%</span>
          </div>
        </div>
        <div class="col" id="vatAmountCol" style="display:none;">
          <label for="vatAmount">VAT (auto)</label>
          <input id="vatAmount" type="number" step="0.01" readonly />
        </div>
        <div class="col" id="netAmountCol" style="display:none;">
          <label for="netAmount">Net (auto)</label>
          <input id="netAmount" type="number" step="0.01" readonly />
        </div>
        <div class="col">
          <button id="addBtn">Add Entry</button>
        </div>
      </div>
    </div>
  </section>

  <section id="tab-entries" class="tab">
    <div class="card pad">
      <table>
        <thead>
          <tr>
            <th>Date</th>
            <th>Type</th>
            <th>Category</th>
            <th>Customer</th>
            <th>Gross (€)</th>
            <th>VAT (€)</th>
            <th>Net (€)</th>
            <th>Description</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody id="tbody"></tbody>
      </table>
    </div>
  </section>

  <section id="tab-charts" class="tab">
    <div class="card pad">
      <div class="row" style="align-items:center; margin-bottom:8px;">
        <div class="col" style="min-width:220px;">
          <strong>Charts by Category</strong>
          <div class="badge">Reflects current filters</div>
        </div>
        <div class="col" style="max-width:220px;">
          <label for="metric">Metric</label>
          <select id="metric">
            <option value="income">Income (Net)</option>
            <option value="expense">Expense</option>
            <option value="net">Net (Income − Expense)</option>
          </select>
        </div>
        <div class="col" style="max-width:200px;">
          <label for="topN">Top N categories</label>
          <input id="topN" type="number" min="1" step="1" value="12" />
        </div>
      </div>
      <div class="sep"></div>
      <div class="chart-wrap"><canvas id="catChart" width="1000" height="360"></canvas></div>
      <div class="legend" style="margin-top:8px;"><span class="dot"></span><span id="legendText">Income (Net) by category (€)</span></div>
    </div>
  </section>

  <section id="tab-customers" class="tab">
    <div class="card pad">
      <div class="row" style="align-items:flex-end; margin-bottom:8px;">
        <div class="col" style="min-width:220px;">
          <strong>Customers</strong>
          <div class="badge">Manage your customers and see totals</div>
        </div>
        <div class="col">
          <label for="c_name">Name</label>
          <input id="c_name" placeholder="e.g., Alice Janssens" />
        </div>
        <div class="col">
          <label for="c_phone">Phone</label>
          <input id="c_phone" placeholder="+32 ..." />
        </div>
        <div class="col">
          <label for="c_email">Email</label>
          <input id="c_email" type="email" placeholder="<EMAIL>" />
        </div>
        <div class="col">
          <label for="c_nationality">Nationality</label>
          <input id="c_nationality" placeholder="e.g., Belgian" />
        </div>
        <div class="col">
          <label for="c_instagram">Instagram</label>
          <input id="c_instagram" placeholder="@username or https://instagram.com/username" />
        </div>
        <div class="col">
          <label for="c_website">Website</label>
          <input id="c_website" placeholder="https://example.com" />
        </div>
        <div class="col">
          <label for="c_facebook">Facebook</label>
          <input id="c_facebook" placeholder="https://facebook.com/username" />
        </div>
        <div class="col">
          <label for="c_note">Note</label>
          <input id="c_note" placeholder="Optional note" />
        </div>
        <div class="col">
          <label for="c_birthday">Birthday</label>
          <input id="c_birthday" type="date" />
        </div>
        <div class="col">
          <label for="c_wedding">Wedding Date</label>
          <input id="c_wedding" type="date" />
        </div>
        <div class="col" style="flex:0 0 160px;">
          <button id="addCustomerBtn">Add Customer</button>
        </div>
      </div>

      <div class="sep"></div>
      <div class="row" style="align-items:flex-end; margin-bottom:8px;">
        <div class="col" style="min-width:220px;">
          <strong>Birthday / Wedding Finder</strong>
          <div class="badge">Pick a month to see all birthdays and weddings (year ignored)</div>
        </div>
        <div class="col" style="max-width:220px;">
          <label for="ev_month">Month</label>
          <input id="ev_month" type="month" />
        </div>
        <div class="col" style="flex:0 0 140px;">
          <button id="ev_clear" class="minor">Clear</button>
        </div>
      </div>

      <div class="table-scroll">
        <table id="eventsTable">
          <thead><tr><th>Type</th><th>Date</th><th>Name</th><th>Phone</th><th>Email</th><th>Note</th></tr></thead>
          <tbody id="eventsBody"></tbody>
        </table>
      </div>

      <div class="sep"></div>
      <table aria-label="Customers list">
        <thead>
          <tr>
            <th>Name</th>
            <th>Phone</th>
            <th>Email</th>
            <th>Nationality</th>
            <th>Social</th>
            <th>Birthday</th>
            <th>Wedding</th>
            <th>Purchases</th>
            <th>Total Spent (Net €)</th>
            <th>VAT Collected (€)</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody id="customersTbody"></tbody>
      </table>
    </div>
  </section>

  <section id="tab-categories" class="tab">
    <div class="card pad">
      <div class="row" style="align-items:flex-end; margin-bottom:8px;">
        <div class="col" style="min-width:220px;">
          <strong>Categories</strong>
          <div class="badge">Add, rename, or delete categories</div>
        </div>
        <div class="col">
          <label for="cat_new">New category</label>
          <input id="cat_new" placeholder="e.g., Printing, Studio Rent" />
        </div>
        <div class="col" style="flex:0 0 160px;">
          <button id="addCategoryBtn">Add Category</button>
        </div>
      </div>
      <div class="sep"></div>
      <table>
        <thead><tr><th>Name</th><th>Entries count</th><th>Actions</th></tr></thead>
        <tbody id="categoriesTbody"></tbody>
      </table>
    </div>
  </section>

  <section id="tab-report" class="tab">
    <div class="card pad">
      <div class="row" style="align-items:center; margin-bottom:8px;">
        <div class="col" style="min-width:240px;">
          <strong>Monthly Report — Income only</strong>
          <div class="badge">Uses the selected Month</div>
        </div>
        <div class="col" style="max-width:240px;">
          <label for="reportMetric">Chart metric</label>
          <select id="reportMetric">
            <option value="net">Net Income</option>
            <option value="vat">VAT</option>
          </select>
        </div>
      </div>
      <div class="sep"></div>
      <div class="table-scroll">
        <table id="reportTable">
          <thead id="reportHead"></thead>
          <tbody id="reportBody"></tbody>
          <tfoot id="reportFoot"></tfoot>
        </table>
      </div>
      <div class="chart-wrap" style="margin-top:12px;">
        <canvas id="reportChart" width="1000" height="360"></canvas>
      </div>
    </div>
  </section>

  
  <section id="tab-daily" class="tab">
    <div class="card pad">
      <div class="row" style="align-items:center; margin-bottom:8px;">
        <div class="col" style="min-width:240px;">
          <strong>Daily Report</strong>
          <div class="badge">Uses the selected Month</div>
        </div>
        <div class="col" style="max-width:220px;">
          <label for="daily_metric">Metric</label></div>
        <div class="col" style="max-width:220px;">
          <label for="daily_month">Month</label>
          <input id="daily_month" type="month">
        <div class="hint"></div><div class="col">
          <select id="daily_metric">
            <option value="net">Net (Income − Expense)</option>
            <option value="income_net">Income (Net)</option>
            <option value="income_vat">VAT (Income)</option>
            <option value="expense">Expense</option>
          </select>
        </div>
        <div class="col" style="flex:0 0 160px;">
          <button id="daily_export_csv" class="minor">Export CSV</button>
        </div>
      </div>
      <div class="sep"></div>
      <div class="table-scroll">
        <table id="daily_table">
          <thead>
            <tr>
              <th>Date</th>
              <th>Income Gross</th>
              <th>Income VAT</th>
              <th>Income Net</th>
              <th>Expense</th>
              <th>Day Net</th>
            </tr>
          </thead>
          <tbody id="daily_body"></tbody>
          <tfoot id="daily_foot"></tfoot>
        </table>
      </div>
      <div class="chart-wrap" style="margin-top:12px;">
        <canvas id="daily_chart" width="1000" height="360"></canvas>
      </div>
    </div>
  </section>

  <section id="tab-topcustomers" class="tab">
    <div class="card pad">
      <div class="row" style="align-items:center; margin-bottom:8px;">
        <div class="col" style="min-width:240px;">
          <strong>Top Customers — Lifetime (Income only)</strong>
          <div class="badge">Ignores month & search filters</div>
        </div>
        <div class="col" style="max-width:200px;">
          <label for="tc_metric">Chart metric</label>
          <select id="tc_metric">
            <option value="net">Net</option>
            <option value="gross">Gross</option>
            <option value="vat">VAT</option>
          </select>
        </div>
        <div class="col" style="max-width:160px;">
          <label for="tc_topn">Top N</label>
          <input id="tc_topn" type="number" min="1" step="1" value="10" />
        </div>
      </div>
      <div class="sep"></div>
      <div class="table-scroll">
        <table id="tc_table">
          <thead><tr><th>Customer</th><th>Purchases</th><th>Gross (€)</th><th>VAT (€)</th><th>Net (€)</th></tr></thead>
          <tbody id="tc_body"></tbody>
        </table>
      </div>
      <div class="chart-wrap" style="margin-top:12px;">
        <canvas id="tc_chart" width="1000" height="360"></canvas>
      </div>
    </div>
  </section>

</div>

<script>
(function(){
  // Compatibility: randomUUID polyfill
  try{
    if(!('crypto' in window)) window.crypto = {};
    if(typeof window.crypto.randomUUID !== 'function'){
      window.crypto.randomUUID = function(){
        return 'id-' + Date.now() + '-' + Math.random().toString(16).slice(2);
      };
    }
  }catch(e){}

  var $ = function(s){ return document.querySelector(s); };
  var $$ = function(s){ return Array.from(document.querySelectorAll(s)); };

  var STORAGE_KEY="soa_entries_v1";
  var CUSTOMER_KEY="soa_customers_v1";
  var CATEGORY_KEY="soa_categories_v1";
  var TAB_KEY="soa_active_tab";
  var VAT_LOCKED=true, VAT_LOCKED_RATE=21;

  // Filters
  var monthEl=$("#month"); var searchEl=$("#search");

  // Add form
  var dateEl=$("#date"); var typeEl=$("#type");
  var categoryEl=$("#category"); var catList=$("#catlist"); var quickAddCategoryBtn=$("#quickAddCategory");
  var descEl=$("#description");
  var amountIncomeEl=$("#amount_income"); var amountExpenseEl=$("#amount_expense");
  var customerCol=$("#customerCol"); var customerSelect=$("#customer"); var quickAddCustomerBtn=$("#quickAddCustomer");
  var vatCol=$("#vatCol"); var vatRateEl=$("#vatRate"); var vatAmountEl=$("#vatAmount"); var netAmountEl=$("#netAmount");
  var vatAmountCol=$("#vatAmountCol"); var netAmountCol=$("#netAmountCol");
  var addBtn=$("#addBtn"); var tbody=$("#tbody");

  // Customers form
  var c_name=$("#c_name"), c_phone=$("#c_phone"), c_email=$("#c_email"), c_nationality=$("#c_nationality"), c_note=$("#c_note");
  var c_instagram=$("#c_instagram"), c_website=$("#c_website"), c_facebook=$("#c_facebook");
  var c_birthday=$("#c_birthday"), c_wedding=$("#c_wedding"); var addCustomerBtn=$("#addCustomerBtn");
  var customersTbody=$("#customersTbody");

  // Finder
  var evMonthEl=$("#ev_month"); var evClearBtn=$("#ev_clear"); var eventsBody=$("#eventsBody");

  // KPIs
  var sumIncomeEl=$("#sumIncome"), sumVatEl=$("#sumVat"), sumExpenseEl=$("#sumExpense"), sumBalanceEl=$("#sumBalance");

  // Toolbar
  var exportJsonBtn=$("#exportJsonBtn"), importJsonBtn=$("#importJsonBtn"), importFile=$("#importFile"), exportCsvBtn=$("#exportCsvBtn"), clearBtn=$("#clearBtn");

  // Charts canvas
  var metricEl=$("#metric"), topNEl=$("#topN"); var catChart=$("#catChart"); var legendText=$("#legendText"); var catCtx=catChart.getContext("2d");
  var reportMetricEl=$("#reportMetric"); var reportChart=$("#reportChart"); var reportCtx=reportChart.getContext("2d");
  var tcMetricEl=$("#tc_metric"); var tcTopNEl=$("#tc_topn"); var tcChart=$("#tc_chart"); var tcCtx=tcChart.getContext("2d");

  // Tabs
  var tabButtons=$$(".tab-btn");
  var tabs={dashboard:$("#tab-dashboard"), add:$("#tab-add"), entries:$("#tab-entries"), charts:$("#tab-charts"), customers:$("#tab-customers"), categories:$("#tab-categories"), report:$("#tab-report"), topcustomers:$("#tab-topcustomers")};

  // Helpers
  function formatMoney(n){ try { return new Intl.NumberFormat(undefined,{style:"currency",currency:"EUR"}).format(n||0); } catch(e){ return (n||0).toFixed(2); } }
  function to2(n){ return (Number(n||0)).toFixed(2); }
  function escapeHtml(str){ return String(str||"").replace(/[&<>\"']/g, function(s){ return {"&":"&amp;","<":"&lt;",">":"&gt;","\"":"&quot;","'":"&#39;"}[s]; }); }
  function cleanStr(x){ var s = (x==null? "": String(x)).trim(); var low=s.toLowerCase(); if(!s || low==="nan"||low==="none"||low==="null"||low==="undefined"||low==="nil"||low==="-" ) return ""; return s; }

  function buildSocialChips(c){
    var chips=[];
    var ig=(c.instagram||"").trim() || (c.social||"").trim();
    if(ig){
      var url=ig;
      if(!/^https?:\/\//i.test(ig)){
        var handle=ig.replace(/^@/,"").trim(); if(handle) url="https://instagram.com/"+handle;
      }
      var label="Instagram"; try{ var u=new URL(url); label=(u.hostname||"").replace(/^www\./,""); }catch(e){}
      chips.push('<a class="badge" href="'+url+'" target="_blank" rel="noopener">'+label+'</a>');
    }
    var web=(c.website||"").trim();
    if(web){ var url2=/^https?:\/\//i.test(web)?web:("https://"+web); var label2="Website"; try{ var u2=new URL(url2); label2=(u2.hostname||"").replace(/^www\./,""); }catch(e){} chips.push('<a class="badge" href="'+url2+'" target="_blank" rel="noopener">'+label2+'</a>'); }
    var fb=(c.facebook||"").trim();
    if(fb){ var url3=/^https?:\/\//i.test(fb)?fb:("https://"+fb); chips.push('<a class="badge" href="'+url3+'" target="_blank" rel="noopener">Facebook</a>'); }
    return chips.length? chips.join(" ") : "—";
  }

  // Data persistence
  function sanitizeEntry(e){
    var type = e.type==="expense" ? "expense" : "income";
    var gross = Number((e.gross!=null?e.gross:(e.amount!=null?e.amount:0)));
    var vatRate = (type==="income") ? Number((e.vatRate!=null?e.vatRate:VAT_LOCKED_RATE)) : null;
    if(VAT_LOCKED && type==="income") vatRate = VAT_LOCKED_RATE;
    var vatAmount = (type==="income") ? Number((e.vatAmount!=null?e.vatAmount:(gross * (vatRate||0)/100))) : null;
    var net = (type==="income") ? Number((e.net!=null?e.net:(gross - (vatAmount||0)))) : Number(gross);
    return {
      id: String(e.id || crypto.randomUUID()),
      date: cleanStr(e.date) || new Date().toISOString().slice(0,10),
      type: type,
      category: cleanStr(e.category),
      description: cleanStr(e.description),
      gross: gross, vatRate: vatRate, vatAmount: vatAmount, net: net,
      customerId: e.customerId ? String(e.customerId) : null
    };
  }
  function sanitizeCustomer(c){
    return {
      id: String(c.id || crypto.randomUUID()),
      name: cleanStr(c.name),
      phone: cleanStr(c.phone),
      email: cleanStr(c.email),
      nationality: cleanStr(c.nationality),
      note: cleanStr(c.note),
      social: cleanStr(c.social),
      instagram: cleanStr(c.instagram),
      website: cleanStr(c.website),
      facebook: cleanStr(c.facebook),
      birthday: cleanStr(c.birthday),
      weddingDate: cleanStr(c.weddingDate)
    };
  }
  function loadEntries(){ try{ var raw=localStorage.getItem(STORAGE_KEY); if(!raw) return []; var data=JSON.parse(raw); return Array.isArray(data)? data.map(sanitizeEntry) : (Array.isArray(data.entries)? data.entries.map(sanitizeEntry):[]);}catch(e){return []}}
  function saveEntries(){ localStorage.setItem(STORAGE_KEY, JSON.stringify(entries)); render(); }
  function loadCustomers(){ try{ var raw=localStorage.getItem(CUSTOMER_KEY); if(!raw) return []; var data=JSON.parse(raw); return Array.isArray(data)? data.map(sanitizeCustomer):[];}catch(e){return []} }
  function saveCustomers(){ localStorage.setItem(CUSTOMER_KEY, JSON.stringify(customers)); renderCustomers(); renderCustomerSelect(); render(); }
  function loadCategories(){ try{ var raw=localStorage.getItem(CATEGORY_KEY); if(!raw) return []; var data=JSON.parse(raw); return Array.isArray(data)? data.filter(function(x){return x && typeof x==="string"}):[];}catch(e){return []} }
  function saveCategories(){ localStorage.setItem(CATEGORY_KEY, JSON.stringify(categories)); renderCategoryDatalist(); renderCategories(); render(); }

  // Load
  var entries=loadEntries(), customers=loadCustomers(), categories=loadCategories();
  entries = entries.map(sanitizeEntry); customers = customers.map(sanitizeCustomer);
  localStorage.setItem(STORAGE_KEY, JSON.stringify(entries));
  localStorage.setItem(CUSTOMER_KEY, JSON.stringify(customers));

  // Init defaults
  try{ document.getElementById("date").valueAsDate=new Date(); }catch(e){}
  monthEl.value = new Date().toISOString().slice(0,7);
  evMonthEl.value = monthEl.value;
  try{ vatRateEl.value=String(VAT_LOCKED_RATE); vatRateEl.disabled=true; }catch(e){}

  // Tabs
  function switchTab(name){
    tabButtons.forEach(function(b){ b.setAttribute("aria-selected", String(b.dataset.tab===name)); });
    for(var k in tabs){ if(tabs.hasOwnProperty(k)){ tabs[k].classList.toggle("active", k===name); } }
    try{ localStorage.setItem(TAB_KEY, name); }catch(e){}
    if(name==="charts") drawCategoryChart(getFiltered());
    if(name==="report") renderMonthlyReport();
    if(name==="topcustomers") renderTopCustomers();
  }
  tabButtons.forEach(function(b){ b.addEventListener("click", function(){ switchTab(b.dataset.tab); }); });
  var tabsContainer = document.querySelector('.tabs');
  if(tabsContainer){
    tabsContainer.addEventListener('click', function(ev){
      var btn = ev.target.closest('.tab-btn');
      if(btn && btn.dataset && btn.dataset.tab){ ev.preventDefault(); switchTab(btn.dataset.tab); }
    });
  }
  switchTab((function(){ try{ return localStorage.getItem(TAB_KEY);}catch(e){ return null; }})() || "dashboard");

  // Helpers
  function ensureCustomerByName(name){
    var existing=customers.find(function(c){ return c.name.toLowerCase()===name.toLowerCase(); });
    if(existing) return existing.id;
    var c=sanitizeCustomer({id:crypto.randomUUID(), name:name});
    customers.push(c); saveCustomers(); return c.id;
  }
  function getCustomerName(id){ if(!id) return "—"; var c=customers.find(function(x){return x.id===id}); return c? c.name : "—"; }
  function renderCustomerSelect(){
    var opts = ['<option value="">—</option>'].concat(customers.slice().sort(function(a,b){return a.name.localeCompare(b.name)}).map(function(c){ return '<option value="'+c.id+'">'+escapeHtml(c.name)+'</option>'; }));
    customerSelect.innerHTML = opts.join("");
  }

  // Events finder
  function dateToMonth(str){ if(!str) return ""; var m=String(str).split("-"); return m.length>=2? m[1] : ""; }
  function dateToMonthDay(str){ if(!str) return ""; var m=String(str).split("-"); return m.length<3? "" : (m[1]+"-"+m[2]); }
  function formatNiceDate(str){ if(!str) return "—"; var d=new Date(str); if(isNaN(d)) return str; return d.toLocaleDateString(undefined,{month:"short", day:"2-digit"}); }
  function renderEventsFinder(){
    var monthVal=evMonthEl.value; var monthMM=monthVal? monthVal.split("-")[1] : null; var rows=[];
    if(monthMM){
      customers.forEach(function(c){
        if(c.birthday && dateToMonth(c.birthday)===monthMM) rows.push({type:"Birthday", date:c.birthday, name:c.name, phone:c.phone, email:c.email, note:c.note});
        if(c.weddingDate && dateToMonth(c.weddingDate)===monthMM) rows.push({type:"Wedding", date:c.weddingDate, name:c.name, phone:c.phone, email:c.email, note:c.note});
      });
    }
    rows.sort(function(a,b){ return dateToMonthDay(a.date).localeCompare(dateToMonthDay(b.date)) || a.type.localeCompare(b.type) || a.name.localeCompare(b.name); });
    eventsBody.innerHTML=""; rows.forEach(function(r){ var tr=document.createElement("tr"); tr.innerHTML='<td>'+r.type+'</td><td class="nowrap">'+formatNiceDate(r.date)+'</td><td>'+escapeHtml(r.name)+'</td><td>'+escapeHtml(r.phone)+'</td><td>'+escapeHtml(r.email)+'</td><td>'+escapeHtml(r.note)+'</td>'; eventsBody.appendChild(tr); });
    if(!monthMM){ eventsBody.innerHTML=""; }
  }
  evMonthEl.addEventListener("input", renderEventsFinder); evClearBtn.addEventListener("click", function(){ evMonthEl.value=""; renderEventsFinder(); });

  // Categories
  var cat_new=$("#cat_new"); var addCategoryBtn=$("#addCategoryBtn"); var categoriesTbody=$("#categoriesTbody");
  function renderCategoryDatalist(){ catList.innerHTML = categories.slice().sort(function(a,b){return a.localeCompare(b)}).map(function(c){return '<option value="'+escapeHtml(c)+'"></option>';}).join(""); }
  function addCategory(){ var name=cat_new.value.trim(); if(!name){ alert("Category name is required."); return; } if(categories.map(function(x){return x.toLowerCase()}).includes(name.toLowerCase())){ alert("This category already exists."); return; } categories.push(name); categories.sort(function(a,b){return a.localeCompare(b)}); cat_new.value=""; saveCategories(); }
  function quickAddCategory(){ var name=prompt("New category name:"); if(!name) return; if(categories.map(function(x){return x.toLowerCase()}).includes(name.toLowerCase())){ alert("This category already exists."); return; } categories.push(name.trim()); categories.sort(function(a,b){return a.localeCompare(b)}); saveCategories(); categoryEl.value=name.trim(); }
  function renderCategories(){
    var counts={}; entries.forEach(function(e){ var key=e.category || "(Uncategorized)"; counts[key]=(counts[key]||0)+1; });
    categoriesTbody.innerHTML=""; categories.slice().sort(function(a,b){return a.localeCompare(b)}).forEach(function(c){
      var cnt=counts[c]||0;
      var tr=document.createElement("tr");
      tr.innerHTML='<td>'+escapeHtml(c)+'</td><td class="mono">'+cnt+'</td><td class="actions"><button class="minor" data-act="renameCategory" data-name="'+escapeHtml(c)+'">Rename</button><button class="minor" data-act="deleteCategory" data-name="'+escapeHtml(c)+'">Delete</button></td>';
      categoriesTbody.appendChild(tr);
    });
  }
  categoriesTbody.addEventListener("click", function(ev){
    var btn=ev.target.closest("button"); if(!btn) return; var act=btn.getAttribute("data-act"); var name=btn.getAttribute("data-name");
    if(act==="renameCategory"){
      var newName=prompt('Rename "'+name+'" to:', name); if(newName===null) return; var t=newName.trim(); if(!t){ alert("Name cannot be empty."); return; }
      if(categories.map(function(x){return x.toLowerCase()}).includes(t.toLowerCase()) && t.toLowerCase()!==name.toLowerCase()){ alert("A category with this name already exists."); return; }
      categories=categories.map(function(c){ return c===name? t : c }); entries=entries.map(function(e){ return e.category===name? Object.assign({}, e, {category:t}) : e }); saveCategories(); saveEntries();
    }else if(act==="deleteCategory"){
      if(!confirm('Delete category "'+name+'"? You can move its entries to another category.')) return;
      var target=prompt("Move existing entries to which category? (leave blank for '(Uncategorized)')",""); if(target===null) return; target=target.trim();
      if(target && !categories.map(function(x){return x.toLowerCase()}).includes(target.toLowerCase())){ if(confirm('Create new category "'+target+'"?')){ categories.push(target); categories.sort(function(a,b){return a.localeCompare(b)}); }else{ return; } }
      entries=entries.map(function(e){ return e.category===name? Object.assign({}, e, {category:(target || "(Uncategorized)")}) : e });
      categories=categories.filter(function(c){ return c!==name }); saveCategories(); saveEntries();
    }
  });
  addCategoryBtn.addEventListener("click", addCategory);
  quickAddCategoryBtn.addEventListener("click", quickAddCategory);

  // Add Entry behaviour
  function toggleIncomeExtras(){
    var isIncome=typeEl.value==="income";
    customerCol.style.display = isIncome ? "" : "none";
    vatCol.style.display = isIncome ? "" : "none";
    vatAmountCol.style.display = isIncome ? "" : "none";
    netAmountCol.style.display = isIncome ? "" : "none";
    amountIncomeEl.disabled = !isIncome;
    amountExpenseEl.disabled = isIncome;
    if(isIncome){ recomputeIncomePreview(); amountIncomeEl.focus(); } else { vatAmountEl.value=""; netAmountEl.value=""; amountExpenseEl.focus(); }
  }
  function recomputeIncomePreview(){
    if(typeEl.value!=="income"){ vatAmountEl.value=""; netAmountEl.value=""; return; }
    var gross = parseFloat(amountIncomeEl.value) || 0;
    var rate = VAT_LOCKED ? VAT_LOCKED_RATE : (parseFloat(vatRateEl.value)||0);
    var vat = +(gross * rate / 100).toFixed(2);
    var net = +(gross - vat).toFixed(2);
    vatAmountEl.value = vat ? vat.toFixed(2) : "0.00";
    netAmountEl.value = net ? net.toFixed(2) : "0.00";
  }
  function addEntry(){
    var isIncome = typeEl.value==="income";
    var gross = parseFloat(isIncome ? amountIncomeEl.value : amountExpenseEl.value);
    if(!dateEl.value){ alert("Please select a date."); return; }
    if(isNaN(gross)){ alert("Please enter a valid amount."); return; }
    var selId = isIncome ? (customerSelect.value || "") : "";
    var customerId = isIncome && selId ? selId : null;
    var cat = categoryEl.value.trim();
    if(cat && !categories.map(function(x){return x.toLowerCase()}).includes(cat.toLowerCase())){
      if(confirm('Add new category "'+cat+'" to your list?')){ categories.push(cat); categories.sort(function(a,b){return a.localeCompare(b)}); saveCategories(); }
    }
    var vatRate=null, vatAmount=null, net=null;
    if(isIncome){ vatRate=VAT_LOCKED? VAT_LOCKED_RATE : (parseFloat(vatRateEl.value)||0); vatAmount=+(gross * vatRate/100).toFixed(2); net=+(gross - vatAmount).toFixed(2); }
    else { net=gross; }
    var item=sanitizeEntry({id:crypto.randomUUID(), date:dateEl.value, type:typeEl.value, category:cat, description:descEl.value.trim(), gross:gross, vatRate:vatRate, vatAmount:vatAmount, net:net, customerId:customerId});
    entries.push(item); clearForm(); saveEntries();
  }
  function clearForm(){
    typeEl.value="income"; categoryEl.value=""; descEl.value="";
    amountIncomeEl.value=""; amountExpenseEl.value="";
    customerSelect.value="";
    vatRateEl.value=String(VAT_LOCKED_RATE); vatAmountEl.value=""; netAmountEl.value="";
    toggleIncomeExtras();
  }
  addBtn.addEventListener("click", addEntry);
  typeEl.addEventListener("change", toggleIncomeExtras);
  amountIncomeEl.addEventListener("input", recomputeIncomePreview);
  vatRateEl.addEventListener("input", recomputeIncomePreview);
  amountIncomeEl.addEventListener("keydown", function(e){ if(e.key==="Enter") addEntry(); });
  amountExpenseEl.addEventListener("keydown", function(e){ if(e.key==="Enter") addEntry(); });

  // Entries table render
  function getFiltered(){
    var m=monthEl.value; var q=searchEl.value.trim().toLowerCase();
    return entries.filter(function(e){ return (m? e.date.startsWith(m):true) && (q? (String(e.category).toLowerCase().includes(q) || String(e.description).toLowerCase().includes(q)) : true); });
  }
  function render(){
    toggleIncomeExtras(); renderCategoryDatalist();
    var filtered = getFiltered().sort(function(a,b){ return a.date.localeCompare(b.date) || String(a.category).localeCompare(String(b.category)); });

    var incomeNet = filtered.filter(function(e){return e.type==="income"}).reduce(function(s,e){return s+(e.net||0)},0);
    var incomeVat = filtered.filter(function(e){return e.type==="income"}).reduce(function(s,e){return s+(e.vatAmount||0)},0);
    var expense = filtered.filter(function(e){return e.type==="expense"}).reduce(function(s,e){return s+(e.net||e.gross||0)},0);
    var balance = incomeNet - expense;
    sumIncomeEl.textContent=formatMoney(incomeNet); sumVatEl.textContent=formatMoney(incomeVat); sumExpenseEl.textContent=formatMoney(expense); sumBalanceEl.textContent=formatMoney(balance);

    tbody.innerHTML="";
    filtered.forEach(function(e){
      var tr=document.createElement("tr");
      tr.innerHTML = '<td class="nowrap">'+e.date+'</td>' +
        '<td><span class="tag '+e.type+'">'+e.type+'</span></td>' +
        '<td>'+escapeHtml(e.category)+'</td>' +
        '<td>'+escapeHtml(getCustomerName(e.customerId))+'</td>' +
        '<td class="mono">'+to2(e.gross)+'</td>' +
        '<td class="mono">'+(e.type==="income" ? to2(e.vatAmount) : "—")+'</td>' +
        '<td class="mono">'+to2(e.net)+'</td>' +
        '<td>'+escapeHtml(e.description)+'</td>' +
        '<td class="actions"><button class="minor" data-act="edit" data-id="'+e.id+'">Edit</button><button class="minor" data-act="del" data-id="'+e.id+'">Delete</button></td>';
      tbody.appendChild(tr);
    });

    drawCategoryChart(filtered);
    renderCustomers();
    renderCategories();
    renderMonthlyReport();
    renderTopCustomers();
    renderEventsFinder();
  }
  tbody.addEventListener("click", function(ev){
    var btn=ev.target.closest("button"); if(!btn) return;
    var id=btn.getAttribute("data-id"); var act=btn.getAttribute("data-act");
    if(act==="del"){
      if(confirm("Delete this entry?")){ entries = entries.filter(function(e){ return e.id!==id }); saveEntries(); }
    }else if(act==="edit"){
      var idx=entries.findIndex(function(e){ return e.id===id }); if(idx===-1) return; var e=entries[idx];
      var newDate=prompt("Date (YYYY-MM-DD):", e.date); if(newDate===null) return;
      var newType=prompt("Type (income/expense):", e.type); if(newType===null) return;
      var newCategory=prompt("Category:", e.category); if(newCategory===null) return;
      var newCustomerName=prompt("Customer name (leave blank to detach):", getCustomerName(e.customerId)); if(newCustomerName===null) return;
      var cid = newCustomerName.trim()? ensureCustomerByName(newCustomerName.trim()) : null;
      var newDesc=prompt("Description:", e.description); if(newDesc===null) return;
      var newGrossStr=prompt("Gross amount (number):", String(e.gross)); if(newGrossStr===null) return;
      var newGross=parseFloat(newGrossStr); if(isNaN(newGross)){ alert("Amount must be a number."); return; }
      var vatRate=e.vatRate, vatAmount=e.vatAmount, net=e.net;
      if(newType.toLowerCase()==="income"){ vatRate=VAT_LOCKED? VAT_LOCKED_RATE : (parseFloat(e.vatRate||VAT_LOCKED_RATE)||0); vatAmount=+(newGross*vatRate/100).toFixed(2); net=+(newGross - vatAmount).toFixed(2); }
      else { vatRate=null; vatAmount=null; net=newGross; }
      if(newCategory && !categories.map(function(x){return x.toLowerCase()}).includes(newCategory.toLowerCase())){
        if(confirm('Add new category "'+newCategory+'" to your list?')){ categories.push(newCategory); categories.sort(function(a,b){return a.localeCompare(b)}); saveCategories(); }
      }
      entries[idx]=sanitizeEntry({id:e.id, date:newDate, type:(newType.toLowerCase()==="expense"?"expense":"income"), category:newCategory, customerId:cid, description:newDesc, gross:newGross, vatRate:vatRate, vatAmount:vatAmount, net:net});
      saveEntries();
    }
  });

  // Customers
  function addCustomer(){
    var name=c_name.value.trim(); var phone=c_phone.value.trim(); var email=c_email.value.trim(); var nationality=c_nationality.value.trim();
    var instagram=c_instagram.value.trim(); var website=c_website.value.trim(); var facebook=c_facebook.value.trim();
    var note=c_note.value.trim(); var birthday=c_birthday.value? c_birthday.value : ""; var weddingDate=c_wedding.value? c_wedding.value : "";
    if(!name){ alert("Name is required."); return; }
    var c=sanitizeCustomer({id:crypto.randomUUID(), name:name, phone:phone, email:email, nationality:nationality, instagram:instagram, website:website, facebook:facebook, note:note, birthday:birthday, weddingDate:weddingDate});
    customers.push(c);
    c_name.value=""; c_phone.value=""; c_email.value=""; c_nationality.value=""; c_instagram.value=""; c_website.value=""; c_facebook.value=""; c_note.value=""; c_birthday.value=""; c_wedding.value="";
    saveCustomers(); renderEventsFinder();
  }
  addCustomerBtn.addEventListener("click", addCustomer);

  function renderCustomers(){
    var totals=new Map(); // id -> {count, net, vat}
    entries.forEach(function(e){ if(e.type!=="income" || !e.customerId) return; if(!totals.has(e.customerId)) totals.set(e.customerId,{count:0, net:0, vat:0}); var t=totals.get(e.customerId); t.count+=1; t.net+=(e.net||0); t.vat+=(e.vatAmount||0); });
    customersTbody.innerHTML="";
    customers.slice().sort(function(a,b){ return a.name.localeCompare(b.name) }).forEach(function(c){
      var t=totals.get(c.id) || {count:0, net:0, vat:0};
      var tr=document.createElement("tr");
      tr.innerHTML = '<td><div>'+escapeHtml(c.name)+'</div>'+(c.note? '<div class="badge">'+escapeHtml(c.note)+'</div>':"")+'</td>' +
        '<td>'+escapeHtml(c.phone)+'</td>' +
        '<td>'+escapeHtml(c.email)+'</td>' +
        '<td>'+(escapeHtml(c.nationality)||"—")+'</td>' +
        '<td>'+buildSocialChips(c)+'</td>' +
        '<td>'+(c.birthday? escapeHtml(formatNiceDate(c.birthday)):"—")+'</td>' +
        '<td>'+(c.weddingDate? escapeHtml(formatNiceDate(c.weddingDate)):"—")+'</td>' +
        '<td class="mono">'+t.count+'</td>' +
        '<td class="mono">'+to2(t.net)+'</td>' +
        '<td class="mono">'+to2(t.vat)+'</td>' +
        '<td class="actions"><button class="minor" data-act="editCustomer" data-id="'+c.id+'">Edit</button><button class="minor" data-act="delCustomer" data-id="'+c.id+'">Delete</button></td>';
      customersTbody.appendChild(tr);
    });
  }
  customersTbody.addEventListener("click", function(ev){
    var btn=ev.target.closest("button"); if(!btn) return;
    var id=btn.getAttribute("data-id"); var act=btn.getAttribute("data-act");
    if(act==="delCustomer"){
      if(confirm("Delete this customer? Entries linked remain, but link will be removed.")){
        customers = customers.filter(function(c){ return c.id!==id });
        entries = entries.map(function(e){ return e.customerId===id? Object.assign({}, e, {customerId:null}) : e });
        saveCustomers(); saveEntries(); renderEventsFinder();
      }
    }else if(act==="editCustomer"){
      var idx=customers.findIndex(function(c){ return c.id===id }); if(idx===-1) return; var c=customers[idx];
      var name=prompt("Name:", c.name); if(name===null) return;
      var phone=prompt("Phone:", c.phone); if(phone===null) return;
      var email=prompt("Email:", c.email); if(email===null) return;
      var nationality=prompt("Nationality (English):", c.nationality || ""); if(nationality===null) return;
      var note=prompt("Note:", c.note); if(note===null) return;
      var instagram=prompt("Instagram (@handle or URL):", c.instagram || c.social || ""); if(instagram===null) return;
      var website=prompt("Website (URL):", c.website || ""); if(website===null) return;
      var facebook=prompt("Facebook (URL):", c.facebook || ""); if(facebook===null) return;
      var birthday=prompt("Birthday (YYYY-MM-DD):", c.birthday || ""); if(birthday===null) return;
      var weddingDate=prompt("Wedding Date (YYYY-MM-DD):", c.weddingDate || ""); if(weddingDate===null) return;
      customers[idx]=sanitizeCustomer({id:c.id, name:name, phone:phone, email:email, nationality:nationality.trim(), note:note, instagram:instagram.trim(), website:website.trim(), facebook:facebook.trim(), birthday:String(birthday||"").trim(), weddingDate:String(weddingDate||"").trim()});
      saveCustomers(); renderEventsFinder();
    }
  });

  // Charts by category
  function drawCategoryChart(list){
    var metric=metricEl.value; var topN=Math.max(1, parseInt(topNEl.value||"12",10));
    var totals={};
    list.forEach(function(e){ var key=e.category || "(Uncategorized)"; totals[key]=totals[key]||{income:0,expense:0}; if(e.type==="income") totals[key].income+=(e.net||0); else totals[key].expense+=(e.net||e.gross||0); });
    var rows=Object.keys(totals).map(function(cat){ var v=totals[cat]; var net=v.income - v.expense; var val=metric==="income"? v.income : metric==="expense"? v.expense : net; return {cat:cat, val:val}; }).filter(function(r){ return r.val!==0; }).sort(function(a,b){ return Math.abs(b.val)-Math.abs(a.val); }).slice(0,topN);

    legendText.textContent = metric==="income" ? "Income (Net) by category (€)" : metric==="expense" ? "Expense by category (€)" : "Net by category (€)";
    var ctx=catCtx; var canvas=catChart; var dpr=window.devicePixelRatio||1; var rect=canvas.getBoundingClientRect(); var width=Math.max(600,rect.width); var height=rect.height||360;
    canvas.width=Math.floor(width*dpr); canvas.height=Math.floor(height*dpr); ctx.setTransform(dpr,0,0,dpr,0,0); ctx.clearRect(0,0,width,height);
    if(rows.length===0){ ctx.fillStyle="#64748b"; ctx.font="14px system-ui"; ctx.fillText("No data for current filters.", 16, 24); return; }
    var pad={top:20,right:16,bottom:60,left:120}; var innerW=width-pad.left-pad.right; var innerH=height-pad.top-pad.bottom;
    ctx.strokeStyle="#e2e8f0"; ctx.beginPath(); ctx.moveTo(pad.left,pad.top); ctx.lineTo(pad.left,pad.top+innerH); ctx.lineTo(pad.left+innerW,pad.top+innerH); ctx.stroke();
    var maxAbs=1; rows.forEach(function(d){ maxAbs=Math.max(maxAbs, Math.abs(d.val)); });
    var yCount=rows.length; var barGap=8; var barH=Math.max(12,(innerH - barGap*(yCount-1))/yCount);
    ctx.fillStyle="#64748b"; ctx.font="12px system-ui"; var ticks=4; for(var i=0;i<=ticks;i++){ var t=(i/ticks)*maxAbs; var x=pad.left+(i/ticks)*innerW; ctx.strokeStyle="#f1f5f9"; ctx.beginPath(); ctx.moveTo(x,pad.top); ctx.lineTo(x,pad.top+innerH); ctx.stroke(); ctx.fillStyle="#64748b"; ctx.textAlign="center"; ctx.textBaseline="top"; ctx.fillText(new Intl.NumberFormat(undefined,{notation:"compact",maximumFractionDigits:1}).format(t), x, pad.top+innerH+6); }
    for(var j=0;j<rows.length;j++){ var r=rows[j]; var y=pad.top + j*(barH+barGap); ctx.fillStyle="#0f172a"; ctx.textAlign="right"; ctx.textBaseline="middle"; wrapText(ctx, r.cat, pad.left-10, y+barH/2, pad.left-16, 12); var barW=(Math.abs(r.val)/maxAbs)*innerW; ctx.fillStyle = r.val>=0 ? "#10b981" : "#ef4444"; ctx.fillRect(pad.left, y, barW, barH); ctx.fillStyle="#0f172a"; ctx.textAlign="left"; ctx.fillText(formatMoney(r.val), pad.left + barW + 6, y + barH/2); }
  }
  function wrapText(ctx,text,xRight,y,maxWidth,lineHeight){ var words=String(text).split(" "); var line=""; var lines=[]; for(var i=0;i<words.length;i++){ var w=words[i]; var test=line? line+" "+w : w; if(ctx.measureText(test).width>maxWidth){ if(line) lines.push(line); line=w; } else { line=test; } } if(line) lines.push(line); var totalH=lines.length*lineHeight; var startY=y-totalH/2+lineHeight/2; ctx.fillStyle="#0f172a"; ctx.font="12px system-ui"; for(var k=0;k<lines.length;k++){ ctx.fillText(lines[k], xRight, startY + k*lineHeight); } }

  // Monthly report
  var reportHead=$("#reportHead"), reportBody=$("#reportBody"), reportFoot=$("#reportFoot");
  function renderMonthlyReport(){
    var ym=monthEl.value; var list=entries.filter(function(e){ return e.type==="income" && (ym? e.date.startsWith(ym):true) });
    var custs=Array.from(new Set(list.map(function(e){ return getCustomerName(e.customerId) || "(No customer)" }))).sort(function(a,b){return a.localeCompare(b)});
    var cats=Array.from(new Set(list.map(function(e){ return e.category || "(Uncategorized)" }))).sort(function(a,b){return a.localeCompare(b)});
    var M={}; custs.forEach(function(c){ M[c]={}; cats.forEach(function(k){ M[c][k]=0; }); });
    var totalNet=0, totalVat=0; list.forEach(function(e){ var c=getCustomerName(e.customerId)||"(No customer)"; var k=e.category||"(Uncategorized)"; var net=e.net||0; M[c][k]+=net; totalNet+=net; totalVat+=(e.vatAmount||0); });
    reportHead.innerHTML=""; var trh=document.createElement("tr"); trh.innerHTML='<th>Customer \\ Category</th>' + cats.map(function(k){ return '<th class="mono">'+escapeHtml(k)+'</th>';}).join("") + '<th class="mono">Row Total</th>'; reportHead.appendChild(trh);
    reportBody.innerHTML=""; custs.forEach(function(c){ var rowTotal=0; var tds=cats.map(function(k){ var v=M[c][k]||0; rowTotal+=v; return '<td class="mono">'+to2(v)+'</td>'; }).join(""); var tr=document.createElement("tr"); tr.innerHTML='<td>'+escapeHtml(c)+'</td>'+tds+'<td class="mono"><strong>'+to2(rowTotal)+'</strong></td>'; reportBody.appendChild(tr); });
    var trf=document.createElement("tr"); var colTotals=cats.map(function(k){ return custs.reduce(function(s,c){ return s+(M[c][k]||0) },0) }); trf.innerHTML = '<td><strong>Column Total</strong></td>' + colTotals.map(function(v){ return '<td class="mono"><strong>'+to2(v)+'</strong></td>'; }).join("") + '<td class="mono"><strong>'+to2(totalNet)+'</strong></td>'; reportFoot.innerHTML=""; reportFoot.appendChild(trf);
    drawReportChart(custs, list);
  }
  function drawReportChart(custs, list){
    var metric=reportMetricEl.value; var map=new Map();
    list.forEach(function(e){ var name=getCustomerName(e.customerId) || "(No customer)"; map.set(name, (map.get(name)||0) + (metric==="vat" ? (e.vatAmount||0) : (e.net||0))); });
    var rows=Array.from(map.entries()).map(function(p){ return {name:p[0], val:p[1]} }).sort(function(a,b){ return b.val - a.val; });
    var canvas=reportChart, ctx=reportCtx; var dpr=window.devicePixelRatio||1; var rect=canvas.getBoundingClientRect(); var width=Math.max(600,rect.width); var height=rect.height||360;
    canvas.width=Math.floor(width*dpr); canvas.height=Math.floor(height*dpr); ctx.setTransform(dpr,0,0,dpr,0,0); ctx.clearRect(0,0,width,height);
    if(rows.length===0){ ctx.fillStyle="#64748b"; ctx.font="14px system-ui"; ctx.fillText("No income data for selected month.", 16, 24); return; }
    var pad={top:20,right:16,bottom:60,left:160}; var innerW=width-pad.left-pad.right; var innerH=height-pad.top-pad.bottom;
    ctx.strokeStyle="#e2e8f0"; ctx.beginPath(); ctx.moveTo(pad.left,pad.top); ctx.lineTo(pad.left,pad.top+innerH); ctx.lineTo(pad.left+innerW,pad.top+innerH); ctx.stroke();
    var max=1; rows.forEach(function(r){ max=Math.max(max, Math.abs(r.val)); });
    var yCount=rows.length; var barGap=8; var barH=Math.max(12,(innerH - barGap*(yCount-1))/yCount);
    ctx.fillStyle="#64748b"; ctx.font="12px system-ui"; var ticks=4; for(var i=0;i<=ticks;i++){ var t=(i/ticks)*max; var x=pad.left+(i/ticks)*innerW; ctx.strokeStyle="#f1f5f9"; ctx.beginPath(); ctx.moveTo(x,pad.top); ctx.lineTo(x,pad.top+innerH); ctx.stroke(); ctx.fillStyle="#64748b"; ctx.textAlign="center"; ctx.textBaseline="top"; ctx.fillText(new Intl.NumberFormat(undefined,{notation:"compact",maximumFractionDigits:1}).format(t), x, pad.top+innerH+6); }
    for(var i=0;i<rows.length;i++){ var name=rows[i].name; var val=rows[i].val; var y=pad.top + i*(barH+barGap); ctx.fillStyle="#0f172a"; ctx.textAlign="right"; ctx.textBaseline="middle"; wrapText(ctx, name, pad.left-10, y+barH/2, pad.left-16, 12); var barW=(Math.abs(val)/max)*innerW; ctx.fillStyle = metric==="vat" ? "#7c3aed" : "#2563eb"; ctx.fillRect(pad.left, y, barW, barH); ctx.fillStyle="#0f172a"; ctx.textAlign="left"; ctx.fillText(formatMoney(val), pad.left + barW + 6, y + barH/2); }
  }

  // Top customers
  var lastTcData=null;
  function renderTopCustomers(){
    var list=entries.filter(function(e){ return e.type==="income" });
    var map=new Map();
    list.forEach(function(e){ var name=getCustomerName(e.customerId)||"(No customer)"; if(!map.has(name)) map.set(name,{count:0,gross:0,vat:0,net:0}); var t=map.get(name); t.count+=1; t.gross+=(e.gross||0); t.vat+=(e.vatAmount||0); t.net+=(e.net||0); });
    var rows=Array.from(map.entries()).map(function(p){ var name=p[0], v=p[1]; return {name:name, count:v.count, gross:v.gross, vat:v.vat, net:v.net}; }).sort(function(a,b){ return b.net - a.net; });
    var tbody=document.getElementById("tc_body"); tbody.innerHTML=""; rows.forEach(function(r){ var tr=document.createElement("tr"); tr.innerHTML='<td>'+escapeHtml(r.name)+'</td><td class="mono">'+r.count+'</td><td class="mono">'+to2(r.gross)+'</td><td class="mono">'+to2(r.vat)+'</td><td class="mono">'+to2(r.net)+'</td>'; tbody.appendChild(tr); });
    var metric=tcMetricEl.value; var topN=Math.max(1, parseInt(tcTopNEl.value||"10",10)); var sorted=rows.slice().sort(function(a,b){ return (b[metric]-a[metric]) }).slice(0,topN);
    lastTcData = sorted.map(function(r){ return {label:r.name, value:r[metric]} }); drawTopCustomersChart(lastTcData);
  }
  function drawTopCustomersChart(data){
    var canvas=tcChart, ctx=tcCtx; var dpr=window.devicePixelRatio||1; var rect=canvas.getBoundingClientRect(); var width=Math.max(600,rect.width); var height=rect.height||360;
    canvas.width=Math.floor(width*dpr); canvas.height=Math.floor(height*dpr); ctx.setTransform(dpr,0,0,dpr,0,0); ctx.clearRect(0,0,width,height);
    if(!data || data.length===0){ ctx.fillStyle="#64748b"; ctx.font="14px system-ui"; ctx.fillText("No customer data yet.", 16, 24); return; }
    var pad={top:20,right:16,bottom:60,left:200}; var innerW=width-pad.left-pad.right; var innerH=height-pad.top-pad.bottom;
    ctx.strokeStyle="#e2e8f0"; ctx.beginPath(); ctx.moveTo(pad.left,pad.top); ctx.lineTo(pad.left,pad.top+innerH); ctx.lineTo(pad.left+innerW,pad.top+innerH); ctx.stroke();
    var max=1; data.forEach(function(d){ max=Math.max(max, Math.abs(d.value)); });
    var yCount=data.length; var barGap=8; var barH=Math.max(12,(innerH - barGap*(yCount-1))/yCount);
    ctx.fillStyle="#64748b"; ctx.font="12px system-ui"; var ticks=4; for(var i=0;i<=ticks;i++){ var t=(i/ticks)*max; var x=pad.left+(i/ticks)*innerW; ctx.strokeStyle="#f1f5f9"; ctx.beginPath(); ctx.moveTo(x,pad.top); ctx.lineTo(x,pad.top+innerH); ctx.stroke(); ctx.fillStyle="#64748b"; ctx.textAlign="center"; ctx.textBaseline="top"; ctx.fillText(new Intl.NumberFormat(undefined,{notation:"compact",maximumFractionDigits:1}).format(t), x, pad.top+innerH+6); }
    for(var i=0;i<data.length;i++){ var label=data[i].label; var value=data[i].value; var y=pad.top + i*(barH+barGap); ctx.fillStyle="#0f172a"; ctx.textAlign="right"; ctx.textBaseline="middle"; wrapText(ctx, label, pad.left-10, y+barH/2, pad.left-16, 12); var barW=(Math.abs(value)/max)*innerW; ctx.fillStyle = "#2563eb"; ctx.fillRect(pad.left, y, barW, barH); ctx.fillStyle="#0f172a"; ctx.textAlign="left"; ctx.fillText(formatMoney(value), pad.left + barW + 6, y + barH/2); }
  }

  // Render & listeners
  [monthEl, searchEl, metricEl, topNEl, reportMetricEl, tcMetricEl, tcTopNEl].forEach(function(el){ el.addEventListener("input", render); });
  window.addEventListener("resize", function(){ if(tabs.charts.classList.contains("active")) drawCategoryChart(getFiltered()); if(tabs.report.classList.contains("active")) renderMonthlyReport(); if(tabs.topcustomers.classList.contains("active")) drawTopCustomersChart(lastTcData||[]); });
  quickAddCustomerBtn.addEventListener("click", function(){ var name=prompt("Customer name:"); if(!name) return; var id=ensureCustomerByName(name.trim()); renderCustomerSelect(); customerSelect.value=id; });

  // Export/Import/Clear
  function downloadText(text, filename, mime){ var blob=new Blob([text],{type:mime||"text/plain"}); var url=URL.createObjectURL(blob); var a=document.createElement("a"); a.href=url; a.download=filename; document.body.appendChild(a); a.click(); setTimeout(function(){ document.body.removeChild(a); URL.revokeObjectURL(url);},0); }
  exportJsonBtn.addEventListener("click", function(){ var name=prompt("File name (without extension):",""); if(name===null) return; var safe=(name.trim()||"accounting"); var payload={entries:entries, customers:customers, categories:categories}; downloadText(JSON.stringify(payload,null,2), safe+".json", "application/json"); });
  importJsonBtn.addEventListener("click", function(){ importFile.click(); });
  importFile.addEventListener("change", function(e){
    var file=(e && e.target && e.target.files && e.target.files[0]) ? e.target.files[0] : null;
    if(!file) return; var reader=new FileReader();
    reader.onload=function(){ try{ var data=JSON.parse(String(reader.result)); if(Array.isArray(data)){ entries=data.map(sanitizeEntry);} else if(data && typeof data==="object"){ if(Array.isArray(data.entries)) entries=data.entries.map(sanitizeEntry); if(Array.isArray(data.customers)) customers=data.customers.map(sanitizeCustomer); if(Array.isArray(data.categories)) categories=data.categories.filter(function(x){return x && typeof x==="string"}); } else { throw new Error("Invalid file format."); } localStorage.setItem(STORAGE_KEY, JSON.stringify(entries)); localStorage.setItem(CUSTOMER_KEY, JSON.stringify(customers)); localStorage.setItem(CATEGORY_KEY, JSON.stringify(categories)); renderCustomerSelect(); renderCategoryDatalist(); render(); alert("Import complete."); }catch(err){ alert("Failed to import: "+err.message); } finally { importFile.value=""; } };
    reader.readAsText(file);
  });
  exportCsvBtn.addEventListener("click", function(){ var name=prompt("File name (without extension):",""); if(name===null) return; var safe=(name.trim()||"accounting"); var rows=[["id","date","type","category","customerId","customerName","description","gross","vatRate","vatAmount","net"]].concat(entries.map(function(e){ return [e.id,e.date,e.type,'"'+String(e.category).replace(/"/g,'""')+'"',e.customerId||"",'"'+String(getCustomerName(e.customerId)).replace(/"/g,'""')+'"','"'+String(e.description).replace(/"/g,'""')+'"',e.gross,(e.vatRate!=null?e.vatRate:""), (e.vatAmount!=null?e.vatAmount:""), e.net]; })); var csv=rows.map(function(r){return r.join(",")}).join("\n"); downloadText(csv, safe+".csv", "text/csv"); });
  clearBtn.addEventListener("click", function(){ if(!entries.length && !customers.length && !categories.length){ alert("Nothing to clear."); return; } if(confirm("Delete ALL data (entries, customers, categories)?")){ entries=[]; customers=[]; categories=[]; localStorage.removeItem(STORAGE_KEY); localStorage.removeItem(CUSTOMER_KEY); localStorage.removeItem(CATEGORY_KEY); renderCustomerSelect(); renderCategoryDatalist(); render(); renderCustomers(); renderCategories(); }});

  // Init
  renderCustomerSelect(); renderCategoryDatalist(); renderCustomers(); renderCategories(); render();

})();</script>

<!-- Daily Report: force activate + render -->
<script>
(function(){
  function $(s,root){ return (root||document).querySelector(s); }
  function $all(s,root){ return Array.from((root||document).querySelectorAll(s)); }
  function monthEl(){ return document.getElementById('month') || document.querySelector('input[type=month]'); }
  function dailyMonthEl(){ return document.getElementById('daily_month'); }
  function to2(n){ return (Number(n||0)).toFixed(2); }

  function getEntries(){
    try{
      if(Array.isArray(window.entries)) return window.entries;
      if(window.db && Array.isArray(window.db.entries)) return window.db.entries;
      for (var i=0;i<localStorage.length;i++){
        var k=localStorage.key(i);
        if(!/entries|state|db/i.test(k)) continue;
        try{
          var v=JSON.parse(localStorage.getItem(k));
          if(Array.isArray(v)) return v;
          if(v && Array.isArray(v.entries)) return v.entries;
          if(v && v.db && Array.isArray(v.db.entries)) return v.db.entries;
        }catch(_){}
      }
    }catch(_){}
    return [];
  }
  function daysInMonthStr(ym){
    if(!ym||ym.length<7){ var d=new Date(); return new Date(d.getFullYear(), d.getMonth()+1, 0).getDate(); }
    var p=ym.split('-'); return new Date(+p[0], +p[1], 0).getDate();
  }
  function ensureDailyDom(){
    if($('#daily_body')) return;
    var sec = $('#tab-daily');
    if(!sec){ sec=document.createElement('section'); sec.id='tab-daily'; sec.className='tab'; document.body.appendChild(sec); }
    sec.innerHTML = `
      <div class="card pad">
        <div class="row" style="align-items:center; margin-bottom:8px;">
          <div class="col" style="min-width:240px;">
            <strong>Daily Report</strong>
            <div class="badge">Uses the selected Month</div>
          </div>
          <div class="col" style="max-width:220px;">
            <label for="daily_metric">Metric</label></div>
        <div class="col" style="max-width:220px;">
          <label for="daily_month">Month</label>
          <input id="daily_month" type="month">
        <div class="hint"></div><div class="col">
            <select id="daily_metric">
              <option value="net">Net (Income − Expense)</option>
              <option value="income_net">Income (Net)</option>
              <option value="income_vat">VAT (Income)</option>
              <option value="expense">Expense</option>
            </select>
          </div>
          <div class="col" style="flex:0 0 160px;">
            <button id="daily_export_csv" class="minor">Export CSV</button>
          </div>
        </div>
        <div class="sep"></div>
        <div class="table-scroll">
          <table id="daily_table">
            <thead><tr>
              <th>Date</th><th>Income Gross</th><th>Income VAT</th>
              <th>Income Net</th><th>Expense</th><th>Day Net</th>
            </tr></thead>
            <tbody id="daily_body"></tbody>
            <tfoot id="daily_foot"></tfoot>
          </table>
        </div>
        <div class="chart-wrap" style="margin-top:12px;">
          <canvas id="daily_chart" width="1000" height="360"></canvas>
        </div>
      </div>`;
  }
  function renderDaily(){
    ensureDailyDom();
    var ym = (dailyMonthEl() && dailyMonthEl().value) || (monthEl() && monthEl().value) || new Date().toISOString().slice(0,7);
    var days = daysInMonthStr(ym);
    var list = getEntries();
    var body = $('#daily_body'), foot = $('#daily_foot');
    if(!body||!foot) return;
    var rows=[], totals={ig:0,iv:0,inNet:0,ex:0,dn:0};
    for(var d=1; d<=days; d++){
      var dd=(d<10?'0'+d:String(d)), date=ym+'-'+dd;
      var day = list.filter(e=>e && e.date && e.date.startsWith(date));
      var inc = day.filter(e=>e.type==='income');
      var exp = day.filter(e=>e.type==='expense');
      var ig = inc.reduce((s,e)=>s+Number(e.gross||0),0);
      var iv = inc.reduce((s,e)=>s+Number(e.vatAmount||0),0);
      var inNet = inc.reduce((s,e)=>s+Number(e.net||0),0);
      var ex = exp.reduce((s,e)=>s+Number((e.net!=null?e.net:e.gross)||0),0);
      var dn = inNet - ex;
      rows.push({date,ig,iv,inNet,ex,dn});
      totals.ig+=ig; totals.iv+=iv; totals.inNet+=inNet; totals.ex+=ex; totals.dn+=dn;
    }
    body.innerHTML='';
    rows.forEach(r=>{
      var tr=document.createElement('tr');
      tr.innerHTML = `<td class="nowrap">${r.date}</td>
                      <td class="mono">${to2(r.ig)}</td>
                      <td class="mono">${to2(r.iv)}</td>
                      <td class="mono">${to2(r.inNet)}</td>
                      <td class="mono">${to2(r.ex)}</td>
                      <td class="mono">${to2(r.dn)}</td>`;
      body.appendChild(tr);
    });
    foot.innerHTML = `<tr><td><strong>Total</strong></td>
      <td class="mono"><strong>${to2(totals.ig)}</strong></td>
      <td class="mono"><strong>${to2(totals.iv)}</strong></td>
      <td class="mono"><strong>${to2(totals.inNet)}</strong></td>
      <td class="mono"><strong>${to2(totals.ex)}</strong></td>
      <td class="mono"><strong>${to2(totals.dn)}</strong></td></tr>`;
    drawChart(rows);
  }
  function drawChart(rows){
    var canvas=$('#daily_chart'); if(!canvas) return;
    var ctx=canvas.getContext('2d');
    var metricEl=$('#daily_metric'); var metric=metricEl?metricEl.value:'net';
    var values = rows.map(r=> metric==='income_net'? r.inNet : metric==='income_vat'? r.iv : metric==='expense'? r.ex : r.dn );
    var labels = rows.map(r=>r.date.split('-')[2]);
    var dpr=window.devicePixelRatio||1, rect=canvas.getBoundingClientRect();
    var width=Math.max(600, rect.width||1000), height=rect.height||360;
    canvas.width=Math.floor(width*dpr); canvas.height=Math.floor(height*dpr);
    ctx.setTransform(dpr,0,0,dpr,0,0); ctx.clearRect(0,0,width,height);
    if(values.every(v=>!v)){ ctx.fillStyle='#64748b'; ctx.font='14px system-ui'; ctx.fillText('No data for current month.',16,24); return; }
    var pad={top:20,right:20,bottom:50,left:50}, innerW=width-pad.left-pad.right, innerH=height-pad.top-pad.bottom;
    ctx.strokeStyle='#e2e8f0'; ctx.beginPath(); ctx.moveTo(pad.left,pad.top); ctx.lineTo(pad.left,pad.top+innerH); ctx.lineTo(pad.left+innerW,pad.top+innerH); ctx.stroke();
    var maxVal=1; values.forEach(v=>{ maxVal=Math.max(maxVal, Math.abs(v)); });
    var xStep=innerW/((values.length-1)||1);
    ctx.beginPath();
    values.forEach((v,i)=>{ var x=pad.left+i*xStep, y=pad.top+innerH-(Math.abs(v)/maxVal)*innerH; if(i===0)ctx.moveTo(x,y); else ctx.lineTo(x,y); });
    ctx.strokeStyle='#2563eb'; ctx.lineWidth=2; ctx.stroke();
    ctx.fillStyle='#2563eb'; values.forEach((v,i)=>{ var x=pad.left+i*xStep, y=pad.top+innerH-(Math.abs(v)/maxVal)*innerH; ctx.beginPath(); ctx.arc(x,y,3,0,Math.PI*2); ctx.fill(); });
    ctx.fillStyle='#64748b'; ctx.textAlign='center'; ctx.textBaseline='top'; var step=Math.ceil(values.length/10)||1;
    for(var i=0;i<labels.length;i+=step){ var lx=pad.left+i*xStep; ctx.fillText(labels[i], lx, pad.top+innerH+8); }
  }

  function activateDailyTab(){
    var target = document.getElementById('tab-daily');
    if(!target) return renderDaily();
    // hide others
    $all('section.tab').forEach(s=>s.classList.remove('active'));
    target.classList.add('active');
    renderDaily();
  }

  // Hook tab button
  document.addEventListener('click', function(e){
    var b = e.target.closest('.tab-btn');
    if(b && b.dataset.tab==='daily'){ activateDailyTab(); }
  });
  // Hook month & metric
  document.addEventListener('change', function(e){
    if(e && e.target && e.target.id==='daily_metric') renderDaily();
    if(e && e.target && (e.target.id==='daily_month')) renderDaily();
    if(e && e.target && (e.target.id==='month' || (e.target.type==='month' && e.target.id!=='daily_month'))) renderDaily();
  });

  // Optionally auto-render if daily is pre-selected
  window.addEventListener('load', function(){
    var pre = document.querySelector('.tab-btn[data-tab="daily"][aria-selected="true"]');
    if(pre) activateDailyTab();
  });
})();
</script>

</body>
</html>
