<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Unified Business Management System</title>
  <style>
    :root {
      --bg: #f8fafc;
      --card: #ffffff;
      --ink: #0f172a;
      --muted: #64748b;
      --bd: #e2e8f0;
      --pri: #2563eb;
      --ok: #10b981;
      --err: #ef4444;
      --warn: #f59e0b;
      --vio: #7c3aed;
    }
    
    * { box-sizing: border-box; }
    
    body {
      margin: 0;
      font-family: system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial;
      background: var(--bg);
      color: var(--ink);
      line-height: 1.5;
    }
    
    .app-container {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }
    
    /* Header */
    .app-header {
      background: var(--card);
      border-bottom: 1px solid var(--bd);
      padding: 1rem 2rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    .app-title {
      margin: 0;
      font-size: 1.5rem;
      font-weight: 700;
      color: var(--ink);
    }
    
    .app-subtitle {
      margin: 0.25rem 0 0;
      font-size: 0.875rem;
      color: var(--muted);
    }
    
    /* Navigation */
    .app-nav {
      background: var(--card);
      border-bottom: 1px solid var(--bd);
      padding: 0 2rem;
      overflow-x: auto;
    }
    
    .nav-tabs {
      display: flex;
      gap: 0;
      margin: 0;
      padding: 0;
      list-style: none;
    }
    
    .nav-tab {
      position: relative;
    }
    
    .nav-tab button {
      background: none;
      border: none;
      padding: 1rem 1.5rem;
      font-size: 0.875rem;
      font-weight: 600;
      color: var(--muted);
      cursor: pointer;
      border-bottom: 2px solid transparent;
      transition: all 0.2s ease;
    }
    
    .nav-tab button:hover {
      color: var(--ink);
      background: rgba(37, 99, 235, 0.05);
    }
    
    .nav-tab.active button {
      color: var(--pri);
      border-bottom-color: var(--pri);
    }
    
    /* Main Content */
    .app-main {
      flex: 1;
      padding: 2rem;
      max-width: 1400px;
      margin: 0 auto;
      width: 100%;
    }
    
    .module {
      display: none;
    }
    
    .module.active {
      display: block;
    }
    
    /* Shared Components */
    .card {
      background: var(--card);
      border: 1px solid var(--bd);
      border-radius: 16px;
      padding: 1.5rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    .btn {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.625rem 1rem;
      border: 1px solid var(--bd);
      border-radius: 12px;
      background: var(--card);
      color: var(--ink);
      font-size: 0.875rem;
      font-weight: 600;
      text-decoration: none;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    
    .btn:hover {
      background: var(--bg);
      border-color: var(--pri);
    }
    
    .btn.primary {
      background: var(--pri);
      border-color: var(--pri);
      color: white;
    }
    
    .btn.primary:hover {
      background: #1d4ed8;
      border-color: #1d4ed8;
    }
    
    .btn.success {
      background: var(--ok);
      border-color: var(--ok);
      color: white;
    }
    
    .btn.danger {
      background: var(--err);
      border-color: var(--err);
      color: white;
    }
    
    input, select, textarea {
      width: 100%;
      padding: 0.625rem 0.75rem;
      border: 1px solid var(--bd);
      border-radius: 12px;
      background: var(--card);
      color: var(--ink);
      font: inherit;
      outline: none;
      transition: border-color 0.2s ease;
    }
    
    input:focus, select:focus, textarea:focus {
      border-color: var(--pri);
      box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
    }
    
    label {
      display: block;
      font-size: 0.75rem;
      font-weight: 600;
      color: var(--muted);
      margin-bottom: 0.375rem;
      text-transform: uppercase;
      letter-spacing: 0.025em;
    }
    
    .form-group {
      margin-bottom: 1rem;
    }
    
    .form-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
    }
    
    /* Tab Content */
    .accounting-tab-content {
      display: none;
    }

    .accounting-tab-content.active {
      display: block;
    }

    /* Modal Styles */
    .modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: none;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }

    .modal.active {
      display: flex;
    }

    .modal-content {
      background: var(--card);
      border-radius: 16px;
      padding: 0;
      max-width: 90vw;
      max-height: 90vh;
      overflow-y: auto;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    }

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1.5rem;
      border-bottom: 1px solid var(--bd);
    }

    .modal-body {
      padding: 1.5rem;
    }

    .modal-footer {
      display: flex;
      justify-content: flex-end;
      gap: 0.75rem;
      padding: 1.5rem;
      border-top: 1px solid var(--bd);
    }

    /* Form Enhancements */
    .form-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
    }

    .form-grid-2 {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
    }

    .form-grid-3 {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 1rem;
    }

    .form-grid-4 {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 1rem;
    }

    .checkbox-group {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 0.5rem;
      margin-top: 0.5rem;
    }

    .checkbox-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem;
      border: 1px solid var(--bd);
      border-radius: 8px;
      background: var(--card);
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .checkbox-item:hover {
      background: var(--bg);
      border-color: var(--pri);
    }

    .checkbox-item input[type="checkbox"] {
      width: auto;
      margin: 0;
    }

    /* Items Table */
    .items-table {
      width: 100%;
      border-collapse: separate;
      border-spacing: 0 8px;
      margin: 1rem 0;
    }

    .items-table th {
      font-size: 0.75rem;
      color: var(--muted);
      text-transform: uppercase;
      letter-spacing: 0.025em;
      text-align: left;
      padding: 0 10px;
    }

    .items-table td {
      background: var(--card);
      border: 1px solid var(--bd);
      padding: 8px;
      vertical-align: middle;
    }

    .items-table tr td:first-child {
      border-top-left-radius: 12px;
      border-bottom-left-radius: 12px;
    }

    .items-table tr td:last-child {
      border-top-right-radius: 12px;
      border-bottom-right-radius: 12px;
    }

    .items-table input {
      border: none;
      background: transparent;
      padding: 4px;
      width: 100%;
    }

    .items-table input:focus {
      background: var(--bg);
      border-radius: 4px;
    }

    /* Utility Classes */
    .text-center { text-align: center; }
    .text-right { text-align: right; }
    .text-muted { color: var(--muted); }
    .font-mono { font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Courier New", monospace; }
    .hidden { display: none !important; }
    .full-width { width: 100%; }
    
    /* Responsive */
    @media (max-width: 768px) {
      .app-header, .app-nav, .app-main {
        padding-left: 1rem;
        padding-right: 1rem;
      }
      
      .form-row {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <div class="app-container">
    <!-- Header -->
    <header class="app-header">
      <div>
        <h1 class="app-title">Unified Business Management System</h1>
        <p class="app-subtitle">Integrated Accounting, Lead Management, and Invoicing Solution</p>
      </div>
      <div style="display: flex; gap: 0.5rem; align-items: center;">
        <button class="btn" id="export-all-btn">📤 Export All Data</button>
        <button class="btn" id="import-all-btn">📥 Import Data</button>
        <input type="file" id="import-file" accept="application/json" style="display: none;">
        <div style="font-size: 0.75rem; color: var(--muted); text-align: right;">
          <div>Customers: <span id="header-customer-count">0</span></div>
          <div>Leads: <span id="header-lead-count">0</span></div>
        </div>
      </div>
    </header>
    
    <!-- Navigation -->
    <nav class="app-nav">
      <ul class="nav-tabs">
        <li class="nav-tab active" data-module="accounting">
          <button>📊 Accounting</button>
        </li>
        <li class="nav-tab" data-module="leads">
          <button>🎯 Lead Manager</button>
        </li>
        <li class="nav-tab" data-module="invoices">
          <button>📄 Invoices & Quotes</button>
        </li>
        <li class="nav-tab" data-module="customers">
          <button>👥 Customers</button>
        </li>
      </ul>
    </nav>
    
    <!-- Main Content -->
    <main class="app-main">
      <!-- Accounting Module -->
      <div id="accounting-module" class="module active">
        <div class="card" style="margin-bottom: 1.5rem;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
            <div>
              <h2 style="margin: 0;">Accounting System</h2>
              <p style="margin: 0.5rem 0 0; color: var(--muted);">Track income, expenses, and manage your finances</p>
            </div>
            <div style="display: flex; gap: 0.5rem;">
              <button class="btn" id="export-accounting-btn">Export JSON</button>
              <button class="btn" id="import-accounting-btn">Import JSON</button>
              <button class="btn danger" id="clear-accounting-btn">Clear All</button>
            </div>
          </div>

          <div class="form-row" style="margin-bottom: 1.5rem;">
            <div class="form-group">
              <label>Filter: Month</label>
              <input type="month" id="accounting-month-filter">
            </div>
            <div class="form-group">
              <label>Filter: Search</label>
              <input type="search" id="accounting-search" placeholder="Find in category or description">
            </div>
          </div>
        </div>

        <!-- Accounting Tabs -->
        <div style="display: flex; gap: 8px; margin-bottom: 1.5rem;">
          <button class="btn primary accounting-tab active" data-tab="dashboard">Dashboard</button>
          <button class="btn accounting-tab" data-tab="add">Add Entry</button>
          <button class="btn accounting-tab" data-tab="entries">Entries</button>
          <button class="btn accounting-tab" data-tab="reports">Reports</button>
        </div>

        <!-- Dashboard Tab -->
        <div id="accounting-dashboard" class="accounting-tab-content active">
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1.5rem;">
            <div class="card" style="text-align: center; background: linear-gradient(135deg, var(--ok), #059669);">
              <h3 style="margin: 0 0 0.5rem; font-size: 0.75rem; color: rgba(255,255,255,0.8); text-transform: uppercase;">Total Income (Net)</h3>
              <div style="font-size: 1.5rem; font-weight: 700; color: white; font-family: var(--font-mono);" id="sum-income">€0.00</div>
            </div>
            <div class="card" style="text-align: center; background: linear-gradient(135deg, var(--vio), #7c3aed);">
              <h3 style="margin: 0 0 0.5rem; font-size: 0.75rem; color: rgba(255,255,255,0.8); text-transform: uppercase;">VAT Collected</h3>
              <div style="font-size: 1.5rem; font-weight: 700; color: white; font-family: var(--font-mono);" id="sum-vat">€0.00</div>
            </div>
            <div class="card" style="text-align: center; background: linear-gradient(135deg, var(--err), #dc2626);">
              <h3 style="margin: 0 0 0.5rem; font-size: 0.75rem; color: rgba(255,255,255,0.8); text-transform: uppercase;">Total Expenses</h3>
              <div style="font-size: 1.5rem; font-weight: 700; color: white; font-family: var(--font-mono);" id="sum-expense">€0.00</div>
            </div>
            <div class="card" style="text-align: center; background: linear-gradient(135deg, var(--ink), #1e293b);">
              <h3 style="margin: 0 0 0.5rem; font-size: 0.75rem; color: rgba(255,255,255,0.8); text-transform: uppercase;">Balance (Net - Expenses)</h3>
              <div style="font-size: 1.5rem; font-weight: 700; color: white; font-family: var(--font-mono);" id="sum-balance">€0.00</div>
            </div>
          </div>
        </div>

        <!-- Add Entry Tab -->
        <div id="accounting-add" class="accounting-tab-content">
          <div class="card">
            <h3 style="margin-bottom: 1.5rem;">Add New Entry</h3>
            <form id="accounting-entry-form">
              <div class="form-grid-3">
                <div class="form-group">
                  <label>Date</label>
                  <input type="date" id="entry-date" required>
                </div>
                <div class="form-group">
                  <label>Type</label>
                  <select id="entry-type" required>
                    <option value="income">Income</option>
                    <option value="expense">Expense</option>
                  </select>
                </div>
                <div class="form-group">
                  <label>Category</label>
                  <div style="display: flex; gap: 0.5rem;">
                    <input type="text" id="entry-category" list="category-datalist" placeholder="e.g., Sales, Rent, Equipment" required style="flex: 1;">
                    <button type="button" class="btn" id="quick-add-category-btn">New</button>
                  </div>
                  <datalist id="category-datalist"></datalist>
                </div>
              </div>

              <div class="form-grid-2">
                <div class="form-group">
                  <label>Description</label>
                  <input type="text" id="entry-description" placeholder="Optional note">
                </div>
                <div class="form-group">
                  <label id="amount-label">Amount (Gross)</label>
                  <input type="number" id="entry-amount" step="0.01" min="0" placeholder="0.00" required>
                </div>
              </div>

              <div class="form-grid-2" id="income-fields" style="display: none;">
                <div class="form-group">
                  <label>Customer (for income)</label>
                  <div style="display: flex; gap: 0.5rem;">
                    <select id="entry-customer" style="flex: 1;">
                      <option value="">Select Customer</option>
                    </select>
                    <button type="button" class="btn" id="quick-add-customer-btn">New</button>
                  </div>
                </div>
                <div class="form-group">
                  <label>VAT Rate (%)</label>
                  <div style="display: flex; gap: 0.5rem; align-items: center;">
                    <input type="number" id="entry-vat-rate" value="21" step="0.01" readonly style="flex: 1;">
                    <span style="font-size: 0.75rem; color: var(--muted);">Locked at 21%</span>
                  </div>
                </div>
              </div>

              <div class="form-grid-2" id="vat-calculation-fields" style="display: none;">
                <div class="form-group">
                  <label>VAT Amount (auto)</label>
                  <input type="number" id="entry-vat-amount" step="0.01" readonly>
                </div>
                <div class="form-group">
                  <label>Net Amount (auto)</label>
                  <input type="number" id="entry-net-amount" step="0.01" readonly>
                </div>
              </div>

              <div style="margin-top: 1.5rem; display: flex; gap: 0.75rem;">
                <button type="submit" class="btn primary">Add Entry</button>
                <button type="button" class="btn" id="clear-entry-form-btn">Clear Form</button>
              </div>
            </form>
          </div>
        </div>

        <!-- Entries Tab -->
        <div id="accounting-entries" class="accounting-tab-content">
          <div class="card">
            <h3 style="margin-bottom: 1.5rem;">All Entries</h3>
            <div style="overflow-x: auto;">
              <table style="width: 100%; border-collapse: separate; border-spacing: 0 8px;">
                <thead>
                  <tr style="font-size: 0.75rem; color: var(--muted); text-transform: uppercase; letter-spacing: 0.025em;">
                    <th style="text-align: left; padding: 0 10px;">Date</th>
                    <th style="text-align: left; padding: 0 10px;">Type</th>
                    <th style="text-align: left; padding: 0 10px;">Category</th>
                    <th style="text-align: left; padding: 0 10px;">Customer</th>
                    <th style="text-align: right; padding: 0 10px;">Gross (€)</th>
                    <th style="text-align: right; padding: 0 10px;">VAT (€)</th>
                    <th style="text-align: right; padding: 0 10px;">Net (€)</th>
                    <th style="text-align: left; padding: 0 10px;">Description</th>
                    <th style="text-align: center; padding: 0 10px;">Actions</th>
                  </tr>
                </thead>
                <tbody id="accounting-entries-tbody">
                  <!-- Entries will be inserted here -->
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Reports Tab -->
        <div id="accounting-reports" class="accounting-tab-content">
          <div class="card">
            <h3 style="margin-bottom: 1.5rem;">Financial Reports</h3>
            <p class="text-muted">Advanced reporting features will be implemented here.</p>
          </div>
        </div>
      </div>
      
      <!-- Lead Manager Module -->
      <div id="leads-module" class="module">
        <div class="card" style="margin-bottom: 1.5rem;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
            <div>
              <h2 style="margin: 0;">Lead Management</h2>
              <p style="margin: 0.5rem 0 0; color: var(--muted);">Pipeline stages: New → Contacted → Quote Sent → Follow-Up → Booked → Lost</p>
            </div>
            <div style="display: flex; gap: 0.5rem;">
              <button class="btn primary" id="add-lead-btn">+ Add Lead</button>
              <button class="btn" id="export-leads-btn">Export JSON</button>
              <button class="btn" id="import-leads-btn">Import JSON</button>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>Search Leads</label>
              <input type="search" id="leads-search" placeholder="Search leads (name, phone, city, referrer, instagram)">
            </div>
            <div class="form-group">
              <label>Filter by Occasion</label>
              <select id="leads-occasion-filter">
                <option value="">All occasions</option>
              </select>
            </div>
            <div class="form-group">
              <label>Filter by Service</label>
              <select id="leads-service-filter">
                <option value="">All services</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Lead Pipeline Board -->
        <div id="leads-board" style="display: grid; grid-auto-flow: column; grid-auto-columns: minmax(280px, 1fr); gap: 1rem; min-height: 60vh;">
          <!-- Pipeline columns will be inserted here -->
        </div>
      </div>

      <!-- Lead Modal -->
      <div id="lead-modal" class="modal">
        <div class="modal-content" style="width: 800px;">
          <div class="modal-header">
            <h3 id="lead-modal-title">New Lead</h3>
            <button type="button" class="btn" id="close-lead-modal">✕</button>
          </div>
          <form id="lead-form">
            <div class="modal-body">
              <div class="form-grid-2">
                <div class="form-group">
                  <label>Name *</label>
                  <input type="text" id="lead-name" required placeholder="Full name">
                </div>
                <div class="form-group">
                  <label>Mobile</label>
                  <input type="tel" id="lead-mobile" placeholder="+32 468 24 11 26">
                </div>
              </div>

              <div class="form-grid-2">
                <div class="form-group">
                  <label>Instagram</label>
                  <input type="text" id="lead-instagram" placeholder="@username or full link">
                </div>
                <div class="form-group">
                  <label>Referrer</label>
                  <input type="text" id="lead-referrer" placeholder="Who referred them?">
                </div>
              </div>

              <div class="form-grid-3">
                <div class="form-group">
                  <label>City</label>
                  <input type="text" id="lead-city" placeholder="Ghent">
                </div>
                <div class="form-group">
                  <label>Country</label>
                  <input type="text" id="lead-country" placeholder="Belgium">
                </div>
                <div class="form-group">
                  <label>Occasion</label>
                  <select id="lead-occasion">
                    <option value="">Select occasion</option>
                  </select>
                </div>
              </div>

              <div class="form-group">
                <label>Email</label>
                <input type="email" id="lead-email" placeholder="<EMAIL>">
              </div>

              <div class="form-group">
                <label>Requested Services</label>
                <div id="lead-services" class="checkbox-group">
                  <!-- Service checkboxes will be inserted here -->
                </div>
              </div>

              <div class="form-group">
                <label>Notes / Next Step</label>
                <textarea id="lead-notes" rows="3" placeholder="e.g., send price list; follow up on Friday"></textarea>
              </div>

              <div style="font-size: 0.875rem; color: var(--muted); margin-top: 1rem;">
                <span id="lead-meta-info"></span>
              </div>
            </div>

            <div class="modal-footer">
              <button type="button" class="btn" id="cancel-lead-btn">Cancel</button>
              <button type="submit" class="btn primary">Save Lead</button>
            </div>
          </form>
        </div>
      </div>
      
      <!-- Invoices Module -->
      <div id="invoices-module" class="module">
        <div class="card" style="margin-bottom: 1.5rem;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
            <div>
              <h2 style="margin: 0;">Invoices & Quotes</h2>
              <p style="margin: 0.5rem 0 0; color: var(--muted);">Create professional invoices and quotes</p>
            </div>
            <div style="display: flex; gap: 0.5rem;">
              <button class="btn primary" id="new-invoice-btn">+ New Invoice</button>
              <button class="btn" id="new-quote-btn">+ New Quote</button>
              <button class="btn" id="export-invoices-btn">Export JSON</button>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>Filter by Type</label>
              <select id="invoice-type-filter">
                <option value="">All (Invoices & Quotes)</option>
                <option value="Invoice">Invoices only</option>
                <option value="Quote">Quotes only</option>
              </select>
            </div>
            <div class="form-group">
              <label>Search</label>
              <input type="search" id="invoice-search" placeholder="Number or Customer">
            </div>
            <div class="form-group">
              <label>Currency (display)</label>
              <input type="text" id="invoice-currency" value="€" style="max-width: 80px;">
            </div>
          </div>
        </div>

        <!-- Invoice Stats -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1.5rem;">
          <div class="card" style="text-align: center; background: linear-gradient(135deg, var(--pri), #1d4ed8);">
            <h3 style="margin: 0 0 0.5rem; font-size: 0.75rem; color: rgba(255,255,255,0.8); text-transform: uppercase;">Net (Subtotal)</h3>
            <div style="font-size: 1.5rem; font-weight: 700; color: white; font-family: var(--font-mono);" id="invoice-sum-subtotal">€0.00</div>
          </div>
          <div class="card" style="text-align: center; background: linear-gradient(135deg, var(--vio), #7c3aed);">
            <h3 style="margin: 0 0 0.5rem; font-size: 0.75rem; color: rgba(255,255,255,0.8); text-transform: uppercase;">VAT (Tax)</h3>
            <div style="font-size: 1.5rem; font-weight: 700; color: white; font-family: var(--font-mono);" id="invoice-sum-tax">€0.00</div>
          </div>
          <div class="card" style="text-align: center; background: linear-gradient(135deg, var(--ok), #059669);">
            <h3 style="margin: 0 0 0.5rem; font-size: 0.75rem; color: rgba(255,255,255,0.8); text-transform: uppercase;">Gross (Total)</h3>
            <div style="font-size: 1.5rem; font-weight: 700; color: white; font-family: var(--font-mono);" id="invoice-sum-total">€0.00</div>
          </div>
        </div>

        <!-- Invoice List -->
        <div class="card">
          <h3 style="margin-bottom: 1.5rem;">Documents</h3>
          <div style="overflow-x: auto;">
            <table style="width: 100%; border-collapse: separate; border-spacing: 0 8px;">
              <thead>
                <tr style="font-size: 0.75rem; color: var(--muted); text-transform: uppercase; letter-spacing: 0.025em;">
                  <th style="text-align: left; padding: 0 10px;">Number</th>
                  <th style="text-align: left; padding: 0 10px;">Type</th>
                  <th style="text-align: left; padding: 0 10px;">Issue Date</th>
                  <th style="text-align: left; padding: 0 10px;">Due/Valid</th>
                  <th style="text-align: left; padding: 0 10px;">Customer</th>
                  <th style="text-align: right; padding: 0 10px;">Net</th>
                  <th style="text-align: right; padding: 0 10px;">VAT</th>
                  <th style="text-align: right; padding: 0 10px;">Gross</th>
                  <th style="text-align: center; padding: 0 10px;">Actions</th>
                </tr>
              </thead>
              <tbody id="invoice-table-body">
                <!-- Invoice rows will be inserted here -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
      
      <!-- Customers Module -->
      <div id="customers-module" class="module">
        <div class="card">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
            <h2 style="margin: 0;">Customer Management</h2>
            <button class="btn primary" id="add-customer-btn">+ Add Customer</button>
          </div>

          <div class="form-row" style="margin-bottom: 1.5rem;">
            <div class="form-group">
              <label>Search Customers</label>
              <input type="search" id="customer-search" placeholder="Search by name, email, phone...">
            </div>
            <div class="form-group">
              <label>Filter by Source</label>
              <select id="customer-source-filter">
                <option value="">All Sources</option>
                <option value="lead">From Leads</option>
                <option value="manual">Manual Entry</option>
                <option value="invoice">From Invoices</option>
              </select>
            </div>
          </div>

          <div class="customer-stats" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1.5rem;">
            <div style="background: var(--bg); padding: 1rem; border-radius: 12px; text-align: center;">
              <div style="font-size: 1.5rem; font-weight: 700; color: var(--pri);" id="total-customers">0</div>
              <div style="font-size: 0.875rem; color: var(--muted);">Total Customers</div>
            </div>
            <div style="background: var(--bg); padding: 1rem; border-radius: 12px; text-align: center;">
              <div style="font-size: 1.5rem; font-weight: 700; color: var(--ok);" id="active-customers">0</div>
              <div style="font-size: 0.875rem; color: var(--muted);">Active This Month</div>
            </div>
            <div style="background: var(--bg); padding: 1rem; border-radius: 12px; text-align: center;">
              <div style="font-size: 1.5rem; font-weight: 700; color: var(--warn);" id="total-revenue">€0.00</div>
              <div style="font-size: 0.875rem; color: var(--muted);">Total Revenue</div>
            </div>
          </div>

          <div class="customer-list">
            <table style="width: 100%; border-collapse: separate; border-spacing: 0 8px;">
              <thead>
                <tr style="font-size: 0.75rem; color: var(--muted); text-transform: uppercase; letter-spacing: 0.025em;">
                  <th style="text-align: left; padding: 0 10px;">Name</th>
                  <th style="text-align: left; padding: 0 10px;">Contact</th>
                  <th style="text-align: left; padding: 0 10px;">Location</th>
                  <th style="text-align: left; padding: 0 10px;">Source</th>
                  <th style="text-align: right; padding: 0 10px;">Revenue</th>
                  <th style="text-align: center; padding: 0 10px;">Actions</th>
                </tr>
              </thead>
              <tbody id="customer-table-body">
                <!-- Customer rows will be inserted here -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </main>
  </div>

  <!-- Customer Modal -->
  <div id="customer-modal" class="modal">
    <div class="modal-content" style="width: 700px;">
      <div class="modal-header">
        <h3 id="customer-modal-title">New Customer</h3>
        <button type="button" class="btn" id="close-customer-modal">✕</button>
      </div>
      <form id="customer-form">
        <div class="modal-body">
          <div class="form-grid-2">
            <div class="form-group">
              <label>Name *</label>
              <input type="text" id="customer-name" required placeholder="Full name or company">
            </div>
            <div class="form-group">
              <label>Email</label>
              <input type="email" id="customer-email" placeholder="<EMAIL>">
            </div>
          </div>

          <div class="form-grid-2">
            <div class="form-group">
              <label>Phone</label>
              <input type="tel" id="customer-phone" placeholder="+32 ...">
            </div>
            <div class="form-group">
              <label>VAT Number</label>
              <input type="text" id="customer-vat" placeholder="BE0123456789">
            </div>
          </div>

          <div class="form-group">
            <label>Address</label>
            <input type="text" id="customer-address" placeholder="Street, City, Country">
          </div>

          <div class="form-grid-2">
            <div class="form-group">
              <label>City</label>
              <input type="text" id="customer-city" placeholder="Brussels">
            </div>
            <div class="form-group">
              <label>Country</label>
              <input type="text" id="customer-country" placeholder="Belgium">
            </div>
          </div>

          <div style="font-size: 0.875rem; color: var(--muted); margin-top: 1rem;">
            <span id="customer-meta-info"></span>
          </div>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn" id="cancel-customer-btn">Cancel</button>
          <button type="submit" class="btn primary">Save Customer</button>
        </div>
      </form>
    </div>
  </div>

  <!-- Invoice Modal -->
  <div id="invoice-modal" class="modal">
    <div class="modal-content" style="width: 900px; max-width: 95vw;">
      <div class="modal-header">
        <h3 id="invoice-modal-title">New Invoice</h3>
        <button type="button" class="btn" id="close-invoice-modal">✕</button>
      </div>
      <form id="invoice-form">
        <div class="modal-body">
          <div class="form-grid-3">
            <div class="form-group">
              <label>Type</label>
              <select id="invoice-type" required>
                <option value="Invoice">Invoice</option>
                <option value="Quote">Quote</option>
              </select>
            </div>
            <div class="form-group">
              <label>Number</label>
              <input type="text" id="invoice-number" required readonly>
            </div>
            <div class="form-group">
              <label>Currency</label>
              <input type="text" id="invoice-currency" value="€" required>
            </div>
          </div>

          <div class="form-grid-3">
            <div class="form-group">
              <label>Issue Date</label>
              <input type="date" id="invoice-issue-date" required>
            </div>
            <div class="form-group">
              <label id="invoice-due-label">Due Date</label>
              <input type="date" id="invoice-due-date" required>
            </div>
            <div class="form-group">
              <label>Tax Rate (%)</label>
              <input type="number" id="invoice-tax-rate" value="21" step="0.01" min="0" max="100">
            </div>
          </div>

          <div class="form-grid-2">
            <div class="form-group">
              <label>Customer *</label>
              <div style="display: flex; gap: 0.5rem;">
                <select id="invoice-customer" required style="flex: 1;">
                  <option value="">Select Customer</option>
                </select>
                <button type="button" class="btn" id="invoice-add-customer-btn">New</button>
              </div>
            </div>
            <div class="form-group">
              <label style="display: flex; align-items: center; gap: 0.5rem;">
                <input type="checkbox" id="invoice-apply-tax" checked style="width: auto; margin: 0;">
                Apply Tax
              </label>
            </div>
          </div>

          <div class="form-group">
            <label>Items</label>
            <table class="items-table">
              <thead>
                <tr>
                  <th style="width: 40%;">Description</th>
                  <th style="width: 15%; text-align: right;">Qty</th>
                  <th style="width: 20%; text-align: right;">Unit Price</th>
                  <th style="width: 20%; text-align: right;">Line Total</th>
                  <th style="width: 5%; text-align: center;">—</th>
                </tr>
              </thead>
              <tbody id="invoice-items-body">
                <!-- Items will be inserted here -->
              </tbody>
            </table>
            <button type="button" class="btn" id="add-invoice-item-btn">+ Add Item</button>
          </div>

          <div class="form-grid-2">
            <div class="form-group">
              <label>Notes</label>
              <textarea id="invoice-notes" rows="3" placeholder="Thank you for your business."></textarea>
            </div>
            <div class="form-group">
              <label>Terms</label>
              <textarea id="invoice-terms" rows="3" placeholder="Payment due within 14 days."></textarea>
            </div>
          </div>

          <div style="background: var(--bg); padding: 1rem; border-radius: 12px; margin-top: 1rem;">
            <div class="form-grid-3">
              <div style="text-align: right;">
                <strong>Subtotal: <span id="invoice-subtotal">€0.00</span></strong>
              </div>
              <div style="text-align: right;">
                <strong>Tax: <span id="invoice-tax">€0.00</span></strong>
              </div>
              <div style="text-align: right;">
                <strong>Total: <span id="invoice-total">€0.00</span></strong>
              </div>
            </div>
          </div>

          <div style="font-size: 0.875rem; color: var(--muted); margin-top: 1rem;">
            <span id="invoice-meta-info"></span>
          </div>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn" id="cancel-invoice-btn">Cancel</button>
          <button type="submit" class="btn primary">Save Document</button>
        </div>
      </form>
    </div>
  </div>

  <script>
    // Unified Business Management System
    (function() {
      'use strict';

      // Storage keys
      const STORAGE_KEYS = {
        customers: 'unified_customers_v1',
        leads: 'unified_leads_v1',
        entries: 'unified_entries_v1',
        invoices: 'unified_invoices_v1'
      };

      // Global app state
      window.UnifiedApp = {
        currentModule: 'accounting',
        customers: [],
        leads: [],
        entries: [],
        invoices: [],
        modules: {},

        // Global refresh function to update all modules
        refreshAll() {
          Object.values(this.modules).forEach(module => {
            if (module.render) {
              module.render();
            }
          });

          // Update customer selects in all modules
          if (this.modules.accounting && this.modules.accounting.updateCustomerSelect) {
            this.modules.accounting.updateCustomerSelect();
          }

          if (this.modules.invoices && this.modules.invoices.updateInvoiceCustomerSelect) {
            this.modules.invoices.updateInvoiceCustomerSelect();
          }
        }
      };

      // Utility functions
      function generateId() {
        return 'id-' + Date.now() + '-' + Math.random().toString(36).slice(2);
      }

      function formatMoney(amount) {
        return new Intl.NumberFormat('en-EU', {
          style: 'currency',
          currency: 'EUR'
        }).format(amount || 0);
      }

      function escapeHtml(str) {
        const div = document.createElement('div');
        div.textContent = str || '';
        return div.innerHTML;
      }

      // Data persistence
      function saveData(key, data) {
        try {
          localStorage.setItem(STORAGE_KEYS[key], JSON.stringify(data));
        } catch (e) {
          console.error('Failed to save data:', e);
        }
      }

      function loadData(key) {
        try {
          const data = localStorage.getItem(STORAGE_KEYS[key]);
          return data ? JSON.parse(data) : [];
        } catch (e) {
          console.error('Failed to load data:', e);
          return [];
        }
      }

      // Customer management
      const CustomerManager = {
        init() {
          this.loadCustomers();
          this.bindEvents();
          this.render();
        },

        loadCustomers() {
          window.UnifiedApp.customers = loadData('customers');
        },

        saveCustomers() {
          saveData('customers', window.UnifiedApp.customers);
        },

        addCustomer(customerData) {
          const customer = {
            id: generateId(),
            name: customerData.name || '',
            email: customerData.email || '',
            phone: customerData.phone || '',
            address: customerData.address || '',
            city: customerData.city || '',
            country: customerData.country || '',
            vat: customerData.vat || '',
            source: customerData.source || 'manual',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            ...customerData
          };

          window.UnifiedApp.customers.push(customer);
          this.saveCustomers();
          this.render();
          window.UnifiedApp.refreshAll();
          if (window.updateHeaderCounts) window.updateHeaderCounts();
          return customer;
        },

        updateCustomer(id, updates) {
          const index = window.UnifiedApp.customers.findIndex(c => c.id === id);
          if (index !== -1) {
            window.UnifiedApp.customers[index] = {
              ...window.UnifiedApp.customers[index],
              ...updates,
              updatedAt: new Date().toISOString()
            };
            this.saveCustomers();
            this.render();
            window.UnifiedApp.refreshAll();
          }
        },

        deleteCustomer(id) {
          window.UnifiedApp.customers = window.UnifiedApp.customers.filter(c => c.id !== id);
          this.saveCustomers();
          this.render();
        },

        findCustomerByEmail(email) {
          return window.UnifiedApp.customers.find(c =>
            c.email && c.email.toLowerCase() === email.toLowerCase()
          );
        },

        findCustomerByName(name) {
          return window.UnifiedApp.customers.find(c =>
            c.name && c.name.toLowerCase() === name.toLowerCase()
          );
        },

        bindEvents() {
          const addBtn = document.getElementById('add-customer-btn');
          const searchInput = document.getElementById('customer-search');
          const sourceFilter = document.getElementById('customer-source-filter');

          if (addBtn) {
            addBtn.addEventListener('click', () => this.showCustomerModal());
          }

          if (searchInput) {
            searchInput.addEventListener('input', () => this.render());
          }

          if (sourceFilter) {
            sourceFilter.addEventListener('change', () => this.render());
          }

          this.initCustomerModal();
        },

        initCustomerModal() {
          const modal = document.getElementById('customer-modal');
          const form = document.getElementById('customer-form');
          const closeBtn = document.getElementById('close-customer-modal');
          const cancelBtn = document.getElementById('cancel-customer-btn');

          if (form) {
            form.addEventListener('submit', (e) => {
              e.preventDefault();
              this.handleCustomerFormSubmit();
            });
          }

          if (closeBtn) {
            closeBtn.addEventListener('click', () => this.hideCustomerModal());
          }

          if (cancelBtn) {
            cancelBtn.addEventListener('click', () => this.hideCustomerModal());
          }

          if (modal) {
            modal.addEventListener('click', (e) => {
              if (e.target === modal) {
                this.hideCustomerModal();
              }
            });
          }
        },

        showCustomerModal(customer = null) {
          const modal = document.getElementById('customer-modal');
          const title = document.getElementById('customer-modal-title');
          const form = document.getElementById('customer-form');

          if (!modal || !form) return;

          // Reset form
          form.reset();

          if (customer) {
            // Edit mode
            title.textContent = 'Edit Customer';
            document.getElementById('customer-name').value = customer.name || '';
            document.getElementById('customer-email').value = customer.email || '';
            document.getElementById('customer-phone').value = customer.phone || '';
            document.getElementById('customer-vat').value = customer.vat || '';
            document.getElementById('customer-address').value = customer.address || '';
            document.getElementById('customer-city').value = customer.city || '';
            document.getElementById('customer-country').value = customer.country || '';

            form.dataset.customerId = customer.id;

            const metaInfo = document.getElementById('customer-meta-info');
            if (metaInfo) {
              metaInfo.textContent = `Created: ${new Date(customer.createdAt).toLocaleDateString()}`;
            }
          } else {
            // Add mode
            title.textContent = 'New Customer';
            delete form.dataset.customerId;

            const metaInfo = document.getElementById('customer-meta-info');
            if (metaInfo) {
              metaInfo.textContent = '';
            }
          }

          modal.classList.add('active');
          document.getElementById('customer-name').focus();
        },

        hideCustomerModal() {
          const modal = document.getElementById('customer-modal');
          if (modal) {
            modal.classList.remove('active');
          }
        },

        handleCustomerFormSubmit() {
          const form = document.getElementById('customer-form');
          const customerId = form.dataset.customerId;

          const customerData = {
            name: document.getElementById('customer-name').value.trim(),
            email: document.getElementById('customer-email').value.trim(),
            phone: document.getElementById('customer-phone').value.trim(),
            vat: document.getElementById('customer-vat').value.trim(),
            address: document.getElementById('customer-address').value.trim(),
            city: document.getElementById('customer-city').value.trim(),
            country: document.getElementById('customer-country').value.trim()
          };

          if (!customerData.name) {
            alert('Customer name is required.');
            return;
          }

          if (customerId) {
            // Update existing customer
            this.updateCustomer(customerId, customerData);
          } else {
            // Add new customer
            this.addCustomer(customerData);
          }

          this.hideCustomerModal();
        },

        render() {
          this.updateStats();
          this.renderCustomerTable();
        },

        updateStats() {
          const totalEl = document.getElementById('total-customers');
          const activeEl = document.getElementById('active-customers');
          const revenueEl = document.getElementById('total-revenue');

          // Calculate active customers (those with entries this month)
          const thisMonth = new Date().toISOString().slice(0, 7);
          const activeCustomers = new Set();

          window.UnifiedApp.entries.forEach(entry => {
            if (entry.date.startsWith(thisMonth) && entry.customerId) {
              activeCustomers.add(entry.customerId);
            }
          });

          // Calculate total revenue from accounting entries
          const totalRevenue = window.UnifiedApp.entries
            .filter(entry => entry.type === 'income')
            .reduce((sum, entry) => sum + (entry.netAmount || 0), 0);

          if (totalEl) totalEl.textContent = window.UnifiedApp.customers.length;
          if (activeEl) activeEl.textContent = activeCustomers.size;
          if (revenueEl) revenueEl.textContent = formatMoney(totalRevenue);
        },

        renderCustomerTable() {
          const tbody = document.getElementById('customer-table-body');
          if (!tbody) return;

          const searchTerm = document.getElementById('customer-search')?.value.toLowerCase() || '';
          const sourceFilter = document.getElementById('customer-source-filter')?.value || '';

          let customers = window.UnifiedApp.customers.filter(customer => {
            const matchesSearch = !searchTerm ||
              customer.name.toLowerCase().includes(searchTerm) ||
              (customer.email && customer.email.toLowerCase().includes(searchTerm)) ||
              (customer.phone && customer.phone.includes(searchTerm));

            const matchesSource = !sourceFilter || customer.source === sourceFilter;

            return matchesSearch && matchesSource;
          });

          tbody.innerHTML = customers.map(customer => {
            // Calculate customer revenue from accounting entries
            const customerRevenue = window.UnifiedApp.entries
              .filter(entry => entry.type === 'income' && entry.customerId === customer.id)
              .reduce((sum, entry) => sum + (entry.netAmount || 0), 0);

            return `
              <tr style="background: var(--card); border: 1px solid var(--bd); border-radius: 12px;">
                <td style="padding: 12px; border-top-left-radius: 12px; border-bottom-left-radius: 12px;">
                  <div style="font-weight: 600;">${escapeHtml(customer.name)}</div>
                  ${customer.vat ? `<div style="font-size: 0.75rem; color: var(--muted);">VAT: ${escapeHtml(customer.vat)}</div>` : ''}
                </td>
                <td style="padding: 12px;">
                  <div style="font-size: 0.875rem;">${escapeHtml(customer.email)}</div>
                  <div style="font-size: 0.75rem; color: var(--muted);">${escapeHtml(customer.phone)}</div>
                </td>
                <td style="padding: 12px;">
                  <div style="font-size: 0.875rem;">${escapeHtml(customer.city)}</div>
                  <div style="font-size: 0.75rem; color: var(--muted);">${escapeHtml(customer.country)}</div>
                </td>
                <td style="padding: 12px;">
                  <span style="padding: 0.25rem 0.5rem; background: var(--bg); border-radius: 6px; font-size: 0.75rem;">
                    ${escapeHtml(customer.source)}
                  </span>
                </td>
                <td style="padding: 12px; text-align: right; font-family: var(--font-mono);">
                  ${formatMoney(customerRevenue)}
                </td>
                <td style="padding: 12px; text-align: center; border-top-right-radius: 12px; border-bottom-right-radius: 12px;">
                  <div style="display: flex; gap: 0.25rem; justify-content: center;">
                    <button class="btn" style="padding: 0.25rem 0.5rem; font-size: 0.75rem;"
                            onclick="UnifiedApp.modules.customers.editCustomer('${customer.id}')">
                      Edit
                    </button>
                    <button class="btn primary" style="padding: 0.25rem 0.5rem; font-size: 0.75rem;"
                            onclick="UnifiedApp.modules.customers.createInvoiceForCustomer('${customer.id}')">
                      Invoice
                    </button>
                  </div>
                </td>
              </tr>
            `;
          }).join('');
        },

        editCustomer(id) {
          const customer = window.UnifiedApp.customers.find(c => c.id === id);
          if (customer) {
            this.showCustomerModal(customer);
          }
        },

        createInvoiceForCustomer(customerId) {
          const customer = window.UnifiedApp.customers.find(c => c.id === customerId);
          if (!customer) return;

          // Switch to invoices module and create invoice
          document.querySelector('.nav-tab[data-module="invoices"] button').click();

          // Wait a moment for the module to load, then create invoice
          setTimeout(() => {
            if (window.UnifiedApp.modules.invoices) {
              window.UnifiedApp.modules.invoices.createInvoiceForCustomer(customer);
            }
          }, 100);
        }
      };

      // Accounting management
      const AccountingManager = {
        init() {
          this.loadEntries();
          this.bindEvents();
          this.render();
          this.initTabs();
          this.updateCustomerSelect();
        },

        loadEntries() {
          window.UnifiedApp.entries = loadData('entries');
        },

        saveEntries() {
          saveData('entries', window.UnifiedApp.entries);
        },

        initTabs() {
          const tabs = document.querySelectorAll('.accounting-tab');
          const contents = document.querySelectorAll('.accounting-tab-content');

          tabs.forEach(tab => {
            tab.addEventListener('click', () => {
              const tabId = tab.dataset.tab;

              // Update tab buttons
              tabs.forEach(t => t.classList.toggle('active', t === tab));
              tabs.forEach(t => t.classList.toggle('primary', t === tab));

              // Update tab contents
              contents.forEach(content => {
                content.classList.toggle('active', content.id === `accounting-${tabId}`);
              });
            });
          });
        },

        bindEvents() {
          const form = document.getElementById('accounting-entry-form');
          const typeSelect = document.getElementById('entry-type');
          const amountInput = document.getElementById('entry-amount');
          const clearBtn = document.getElementById('clear-entry-form-btn');
          const quickAddCategoryBtn = document.getElementById('quick-add-category-btn');
          const quickAddCustomerBtn = document.getElementById('quick-add-customer-btn');

          if (form) {
            form.addEventListener('submit', (e) => {
              e.preventDefault();
              this.addEntry();
            });
          }

          if (typeSelect) {
            typeSelect.addEventListener('change', () => this.toggleIncomeFields());
          }

          if (amountInput) {
            amountInput.addEventListener('input', () => this.calculateVAT());
          }

          if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearForm());
          }

          if (quickAddCategoryBtn) {
            quickAddCategoryBtn.addEventListener('click', () => this.quickAddCategory());
          }

          if (quickAddCustomerBtn) {
            quickAddCustomerBtn.addEventListener('click', () => this.quickAddCustomer());
          }

          // Set today's date
          const dateInput = document.getElementById('entry-date');
          if (dateInput) {
            dateInput.valueAsDate = new Date();
          }

          // Set current month filter
          const monthFilter = document.getElementById('accounting-month-filter');
          if (monthFilter) {
            monthFilter.value = new Date().toISOString().slice(0, 7);
            monthFilter.addEventListener('input', () => this.render());
          }

          // Search filter
          const searchInput = document.getElementById('accounting-search');
          if (searchInput) {
            searchInput.addEventListener('input', () => this.render());
          }

          // Initialize categories datalist
          this.updateCategoriesDatalist();
        },

        toggleIncomeFields() {
          const type = document.getElementById('entry-type')?.value;
          const incomeFields = document.getElementById('income-fields');
          const vatCalculationFields = document.getElementById('vat-calculation-fields');
          const amountLabel = document.getElementById('amount-label');

          const isIncome = type === 'income';

          if (incomeFields) {
            incomeFields.style.display = isIncome ? 'grid' : 'none';
          }

          if (vatCalculationFields) {
            vatCalculationFields.style.display = isIncome ? 'grid' : 'none';
          }

          if (amountLabel) {
            amountLabel.textContent = isIncome ? 'Amount (Gross)' : 'Amount';
          }

          this.calculateVAT();
        },

        calculateVAT() {
          const type = document.getElementById('entry-type')?.value;
          const amount = parseFloat(document.getElementById('entry-amount')?.value) || 0;
          const vatRate = parseFloat(document.getElementById('entry-vat-rate')?.value) || 21;

          if (type === 'income' && amount > 0) {
            const vatAmount = (amount * vatRate / 100);
            const netAmount = amount - vatAmount;

            const vatAmountInput = document.getElementById('entry-vat-amount');
            const netAmountInput = document.getElementById('entry-net-amount');

            if (vatAmountInput) vatAmountInput.value = vatAmount.toFixed(2);
            if (netAmountInput) netAmountInput.value = netAmount.toFixed(2);
          }
        },

        updateCustomerSelect() {
          const select = document.getElementById('entry-customer');
          if (!select) return;

          const customers = window.UnifiedApp.customers || [];
          select.innerHTML = '<option value="">Select Customer</option>' +
            customers.map(customer =>
              `<option value="${customer.id}">${escapeHtml(customer.name)}</option>`
            ).join('');
        },

        addEntry() {
          const formData = {
            date: document.getElementById('entry-date')?.value,
            type: document.getElementById('entry-type')?.value,
            category: document.getElementById('entry-category')?.value,
            description: document.getElementById('entry-description')?.value,
            amount: parseFloat(document.getElementById('entry-amount')?.value) || 0,
            customerId: document.getElementById('entry-customer')?.value || null
          };

          if (!formData.date || !formData.type || !formData.category || !formData.amount) {
            alert('Please fill in all required fields.');
            return;
          }

          const entry = {
            id: generateId(),
            ...formData,
            createdAt: new Date().toISOString()
          };

          // Calculate VAT for income entries
          if (formData.type === 'income') {
            const vatRate = 21; // Fixed at 21%
            entry.vatRate = vatRate;
            entry.vatAmount = (formData.amount * vatRate / 100);
            entry.netAmount = formData.amount - entry.vatAmount;
          } else {
            entry.netAmount = formData.amount;
          }

          window.UnifiedApp.entries.push(entry);
          this.saveEntries();
          this.clearForm();
          this.render();

          // Switch to entries tab to show the new entry
          document.querySelector('.accounting-tab[data-tab="entries"]')?.click();
        },

        addEntryFromData(entryData) {
          const entry = {
            id: generateId(),
            ...entryData,
            createdAt: new Date().toISOString()
          };

          // Calculate VAT for income entries
          if (entryData.type === 'income') {
            const vatRate = 21; // Fixed at 21%
            entry.vatRate = vatRate;
            entry.vatAmount = (entryData.amount * vatRate / 100);
            entry.netAmount = entryData.amount - entry.vatAmount;
          } else {
            entry.netAmount = entryData.amount;
          }

          window.UnifiedApp.entries.push(entry);
          this.saveEntries();
          this.render();
        },

        clearForm() {
          const form = document.getElementById('accounting-entry-form');
          if (form) {
            form.reset();
            document.getElementById('entry-date').valueAsDate = new Date();
            this.toggleIncomeFields();
          }
        },

        quickAddCategory() {
          window.UnifiedApp.modules.customers.showCustomerModal();
        },

        quickAddCustomer() {
          window.UnifiedApp.modules.customers.showCustomerModal();
        },

        updateCategoriesDatalist() {
          const datalist = document.getElementById('category-datalist');
          if (!datalist) return;

          // Get unique categories from existing entries
          const categories = [...new Set(window.UnifiedApp.entries.map(e => e.category).filter(Boolean))];

          datalist.innerHTML = categories.map(category =>
            `<option value="${escapeHtml(category)}"></option>`
          ).join('');
        },

        render() {
          this.updateDashboard();
          this.renderEntries();
        },

        updateDashboard() {
          const filtered = this.getFilteredEntries();

          const incomeEntries = filtered.filter(e => e.type === 'income');
          const expenseEntries = filtered.filter(e => e.type === 'expense');

          const totalIncome = incomeEntries.reduce((sum, e) => sum + (e.netAmount || 0), 0);
          const totalVAT = incomeEntries.reduce((sum, e) => sum + (e.vatAmount || 0), 0);
          const totalExpense = expenseEntries.reduce((sum, e) => sum + (e.netAmount || e.amount || 0), 0);
          const balance = totalIncome - totalExpense;

          const sumIncomeEl = document.getElementById('sum-income');
          const sumVatEl = document.getElementById('sum-vat');
          const sumExpenseEl = document.getElementById('sum-expense');
          const sumBalanceEl = document.getElementById('sum-balance');

          if (sumIncomeEl) sumIncomeEl.textContent = formatMoney(totalIncome);
          if (sumVatEl) sumVatEl.textContent = formatMoney(totalVAT);
          if (sumExpenseEl) sumExpenseEl.textContent = formatMoney(totalExpense);
          if (sumBalanceEl) sumBalanceEl.textContent = formatMoney(balance);
        },

        getFilteredEntries() {
          const monthFilter = document.getElementById('accounting-month-filter')?.value;
          const searchTerm = document.getElementById('accounting-search')?.value.toLowerCase() || '';

          return window.UnifiedApp.entries.filter(entry => {
            const matchesMonth = !monthFilter || entry.date.startsWith(monthFilter);
            const matchesSearch = !searchTerm ||
              (entry.category && entry.category.toLowerCase().includes(searchTerm)) ||
              (entry.description && entry.description.toLowerCase().includes(searchTerm));

            return matchesMonth && matchesSearch;
          });
        },

        renderEntries() {
          const tbody = document.getElementById('accounting-entries-tbody');
          if (!tbody) return;

          const entries = this.getFilteredEntries().sort((a, b) =>
            new Date(b.date) - new Date(a.date)
          );

          tbody.innerHTML = entries.map(entry => {
            const customer = entry.customerId ?
              window.UnifiedApp.customers.find(c => c.id === entry.customerId) : null;

            return `
              <tr style="background: var(--card); border: 1px solid var(--bd); border-radius: 12px;">
                <td style="padding: 12px; border-top-left-radius: 12px; border-bottom-left-radius: 12px;">
                  ${entry.date}
                </td>
                <td style="padding: 12px;">
                  <span style="padding: 0.25rem 0.5rem; border-radius: 6px; font-size: 0.75rem; font-weight: 600;
                               background: ${entry.type === 'income' ? 'var(--ok)' : 'var(--err)'};
                               color: white;">
                    ${entry.type}
                  </span>
                </td>
                <td style="padding: 12px;">${escapeHtml(entry.category)}</td>
                <td style="padding: 12px;">${customer ? escapeHtml(customer.name) : '—'}</td>
                <td style="padding: 12px; text-align: right; font-family: var(--font-mono);">
                  ${formatMoney(entry.amount)}
                </td>
                <td style="padding: 12px; text-align: right; font-family: var(--font-mono);">
                  ${entry.type === 'income' ? formatMoney(entry.vatAmount || 0) : '—'}
                </td>
                <td style="padding: 12px; text-align: right; font-family: var(--font-mono);">
                  ${formatMoney(entry.netAmount || entry.amount)}
                </td>
                <td style="padding: 12px;">${escapeHtml(entry.description || '')}</td>
                <td style="padding: 12px; text-align: center; border-top-right-radius: 12px; border-bottom-right-radius: 12px;">
                  <button class="btn" style="padding: 0.25rem 0.5rem; font-size: 0.75rem;"
                          onclick="UnifiedApp.modules.accounting.deleteEntry('${entry.id}')">
                    Delete
                  </button>
                </td>
              </tr>
            `;
          }).join('');
        },

        deleteEntry(id) {
          if (confirm('Delete this entry?')) {
            window.UnifiedApp.entries = window.UnifiedApp.entries.filter(e => e.id !== id);
            this.saveEntries();
            this.render();
          }
        }
      };

      // Lead Manager
      const LeadManager = {
        stages: ['New', 'Contacted', 'Quote Sent', 'Follow-Up', 'Booked', 'Lost'],
        occasions: ['Wedding', 'Engagement', 'Birthday', 'Corporate Event', 'Family', 'Portrait', 'Boudoir', 'Maternity', 'Newborn', 'Other'],
        services: [
          'Wedding Photography', 'Wedding Videography', 'Engagement Photography', 'Event Videography', 'Portrait Session',
          'Studio Session', 'Boudoir', 'Maternity Session', 'Newborn Session', 'Corporate/Promo', 'Product/Branding'
        ],

        init() {
          this.loadLeads();
          this.bindEvents();
          this.initFilters();
          this.render();
        },

        loadLeads() {
          window.UnifiedApp.leads = loadData('leads');
        },

        saveLeads() {
          saveData('leads', window.UnifiedApp.leads);
        },

        initFilters() {
          const occasionFilter = document.getElementById('leads-occasion-filter');
          const serviceFilter = document.getElementById('leads-service-filter');

          if (occasionFilter) {
            occasionFilter.innerHTML = '<option value="">All occasions</option>' +
              this.occasions.map(o => `<option value="${o}">${o}</option>`).join('');
          }

          if (serviceFilter) {
            serviceFilter.innerHTML = '<option value="">All services</option>' +
              this.services.map(s => `<option value="${s}">${s}</option>`).join('');
          }
        },

        bindEvents() {
          const addBtn = document.getElementById('add-lead-btn');
          const searchInput = document.getElementById('leads-search');
          const occasionFilter = document.getElementById('leads-occasion-filter');
          const serviceFilter = document.getElementById('leads-service-filter');

          if (addBtn) {
            addBtn.addEventListener('click', () => this.showLeadModal());
          }

          if (searchInput) {
            searchInput.addEventListener('input', () => this.render());
          }

          if (occasionFilter) {
            occasionFilter.addEventListener('change', () => this.render());
          }

          if (serviceFilter) {
            serviceFilter.addEventListener('change', () => this.render());
          }

          this.initLeadModal();
        },

        initLeadModal() {
          const modal = document.getElementById('lead-modal');
          const form = document.getElementById('lead-form');
          const closeBtn = document.getElementById('close-lead-modal');
          const cancelBtn = document.getElementById('cancel-lead-btn');

          if (form) {
            form.addEventListener('submit', (e) => {
              e.preventDefault();
              this.handleLeadFormSubmit();
            });
          }

          if (closeBtn) {
            closeBtn.addEventListener('click', () => this.hideLeadModal());
          }

          if (cancelBtn) {
            cancelBtn.addEventListener('click', () => this.hideLeadModal());
          }

          if (modal) {
            modal.addEventListener('click', (e) => {
              if (e.target === modal) {
                this.hideLeadModal();
              }
            });
          }

          this.initLeadFormOptions();
        },

        initLeadFormOptions() {
          // Populate occasion dropdown
          const occasionSelect = document.getElementById('lead-occasion');
          if (occasionSelect) {
            occasionSelect.innerHTML = '<option value="">Select occasion</option>' +
              this.occasions.map(o => `<option value="${o}">${o}</option>`).join('');
          }

          // Populate services checkboxes
          const servicesContainer = document.getElementById('lead-services');
          if (servicesContainer) {
            servicesContainer.innerHTML = this.services.map(service => `
              <div class="checkbox-item">
                <input type="checkbox" id="service-${service.replace(/\s+/g, '-')}" value="${service}">
                <label for="service-${service.replace(/\s+/g, '-')}" style="margin: 0; cursor: pointer;">${service}</label>
              </div>
            `).join('');
          }
        },

        showLeadModal(lead = null) {
          const modal = document.getElementById('lead-modal');
          const title = document.getElementById('lead-modal-title');
          const form = document.getElementById('lead-form');

          if (!modal || !form) return;

          // Reset form
          form.reset();

          // Clear all service checkboxes
          const serviceCheckboxes = document.querySelectorAll('#lead-services input[type="checkbox"]');
          serviceCheckboxes.forEach(cb => cb.checked = false);

          if (lead) {
            // Edit mode
            title.textContent = 'Edit Lead';
            document.getElementById('lead-name').value = lead.name || '';
            document.getElementById('lead-mobile').value = lead.mobile || '';
            document.getElementById('lead-instagram').value = lead.instagram || '';
            document.getElementById('lead-referrer').value = lead.referrer || '';
            document.getElementById('lead-city').value = lead.city || '';
            document.getElementById('lead-country').value = lead.country || '';
            document.getElementById('lead-occasion').value = lead.occasion || '';
            document.getElementById('lead-email').value = lead.email || '';
            document.getElementById('lead-notes').value = lead.notes || '';

            // Check selected services
            if (lead.services && Array.isArray(lead.services)) {
              lead.services.forEach(service => {
                const checkbox = document.getElementById(`service-${service.replace(/\s+/g, '-')}`);
                if (checkbox) checkbox.checked = true;
              });
            }

            form.dataset.leadId = lead.id;

            const metaInfo = document.getElementById('lead-meta-info');
            if (metaInfo) {
              metaInfo.textContent = `Stage: ${lead.stage} • Created: ${new Date(lead.createdAt).toLocaleDateString()}`;
            }
          } else {
            // Add mode
            title.textContent = 'New Lead';
            delete form.dataset.leadId;

            const metaInfo = document.getElementById('lead-meta-info');
            if (metaInfo) {
              metaInfo.textContent = '';
            }
          }

          modal.classList.add('active');
          document.getElementById('lead-name').focus();
        },

        hideLeadModal() {
          const modal = document.getElementById('lead-modal');
          if (modal) {
            modal.classList.remove('active');
          }
        },

        handleLeadFormSubmit() {
          const form = document.getElementById('lead-form');
          const leadId = form.dataset.leadId;

          // Get selected services
          const selectedServices = [];
          const serviceCheckboxes = document.querySelectorAll('#lead-services input[type="checkbox"]:checked');
          serviceCheckboxes.forEach(cb => selectedServices.push(cb.value));

          const leadData = {
            name: document.getElementById('lead-name').value.trim(),
            mobile: document.getElementById('lead-mobile').value.trim(),
            instagram: document.getElementById('lead-instagram').value.trim(),
            referrer: document.getElementById('lead-referrer').value.trim(),
            city: document.getElementById('lead-city').value.trim(),
            country: document.getElementById('lead-country').value.trim(),
            occasion: document.getElementById('lead-occasion').value,
            email: document.getElementById('lead-email').value.trim(),
            notes: document.getElementById('lead-notes').value.trim(),
            services: selectedServices
          };

          if (!leadData.name) {
            alert('Lead name is required.');
            return;
          }

          if (leadId) {
            // Update existing lead
            this.updateLead(leadId, leadData);
          } else {
            // Add new lead
            this.addLead(leadData);
          }

          this.hideLeadModal();
        },

        addLead(leadData) {
          const lead = {
            id: generateId(),
            name: leadData.name || '',
            mobile: leadData.mobile || '',
            email: leadData.email || '',
            instagram: leadData.instagram || '',
            city: leadData.city || '',
            country: leadData.country || '',
            referrer: leadData.referrer || '',
            occasion: leadData.occasion || '',
            services: leadData.services || [],
            notes: leadData.notes || '',
            stage: 'New',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            ...leadData
          };

          window.UnifiedApp.leads.push(lead);
          this.saveLeads();
          this.render();
          if (window.updateHeaderCounts) window.updateHeaderCounts();
          return lead;
        },

        updateLead(id, updates) {
          const index = window.UnifiedApp.leads.findIndex(l => l.id === id);
          if (index !== -1) {
            window.UnifiedApp.leads[index] = {
              ...window.UnifiedApp.leads[index],
              ...updates,
              updatedAt: new Date().toISOString()
            };
            this.saveLeads();
            this.render();
          }
        },

        deleteLead(id) {
          if (confirm('Delete this lead?')) {
            window.UnifiedApp.leads = window.UnifiedApp.leads.filter(l => l.id !== id);
            this.saveLeads();
            this.render();
          }
        },

        moveLead(id, toStage) {
          this.updateLead(id, { stage: toStage });
        },

        convertLeadToCustomer(id) {
          const lead = window.UnifiedApp.leads.find(l => l.id === id);
          if (!lead) return;

          // Check if customer already exists
          const existingCustomer = window.UnifiedApp.modules.customers.findCustomerByEmail(lead.email) ||
                                   window.UnifiedApp.modules.customers.findCustomerByName(lead.name);

          if (existingCustomer) {
            alert('Customer already exists!');
            return;
          }

          // Create new customer from lead
          const customer = window.UnifiedApp.modules.customers.addCustomer({
            name: lead.name,
            email: lead.email,
            phone: lead.mobile,
            city: lead.city,
            country: lead.country,
            source: 'lead'
          });

          // Update lead to mark as converted
          this.updateLead(id, { convertedToCustomer: customer.id });

          alert('Lead converted to customer successfully!');
        },

        getFilteredLeads() {
          const searchTerm = document.getElementById('leads-search')?.value.toLowerCase() || '';
          const occasionFilter = document.getElementById('leads-occasion-filter')?.value || '';
          const serviceFilter = document.getElementById('leads-service-filter')?.value || '';

          return window.UnifiedApp.leads.filter(lead => {
            const matchesSearch = !searchTerm ||
              lead.name.toLowerCase().includes(searchTerm) ||
              (lead.mobile && lead.mobile.includes(searchTerm)) ||
              (lead.city && lead.city.toLowerCase().includes(searchTerm)) ||
              (lead.referrer && lead.referrer.toLowerCase().includes(searchTerm)) ||
              (lead.instagram && lead.instagram.toLowerCase().includes(searchTerm));

            const matchesOccasion = !occasionFilter || lead.occasion === occasionFilter;
            const matchesService = !serviceFilter || (lead.services && lead.services.includes(serviceFilter));

            return matchesSearch && matchesOccasion && matchesService;
          });
        },

        render() {
          const board = document.getElementById('leads-board');
          if (!board) return;

          board.innerHTML = '';

          this.stages.forEach(stage => {
            const column = this.createStageColumn(stage);
            board.appendChild(column);
          });
        },

        createStageColumn(stage) {
          const filteredLeads = this.getFilteredLeads();
          const stageLeads = filteredLeads.filter(l => l.stage === stage);

          const column = document.createElement('div');
          column.className = 'card';
          column.style.cssText = 'display: flex; flex-direction: column; min-height: 60vh; padding: 1rem;';
          column.dataset.stage = stage;

          // Column header
          const header = document.createElement('div');
          header.style.cssText = 'display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 1px solid var(--bd);';
          header.innerHTML = `
            <div>
              <h3 style="margin: 0; font-size: 1rem;">${stage}</h3>
              <span style="font-size: 0.75rem; color: var(--muted);">${stageLeads.length} leads</span>
            </div>
            <div style="width: 12px; height: 12px; border-radius: 50%; background: ${this.getStageColor(stage)};"></div>
          `;

          // Dropzone
          const dropzone = document.createElement('div');
          dropzone.style.cssText = 'flex: 1; overflow-y: auto;';
          dropzone.dataset.stage = stage;

          // Add drag and drop events
          dropzone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropzone.style.background = 'rgba(37, 99, 235, 0.1)';
          });

          dropzone.addEventListener('dragleave', () => {
            dropzone.style.background = '';
          });

          dropzone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropzone.style.background = '';
            const leadId = e.dataTransfer.getData('text/plain');
            this.moveLead(leadId, stage);
          });

          // Add leads
          if (stageLeads.length === 0) {
            dropzone.innerHTML = '<div style="text-align: center; color: var(--muted); padding: 2rem; border: 2px dashed var(--bd); border-radius: 12px;">Drop leads here</div>';
          } else {
            stageLeads.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
                      .forEach(lead => {
                        dropzone.appendChild(this.createLeadCard(lead));
                      });
          }

          column.appendChild(header);
          column.appendChild(dropzone);

          return column;
        },

        createLeadCard(lead) {
          const card = document.createElement('div');
          card.style.cssText = 'background: white; border: 1px solid var(--bd); border-radius: 12px; padding: 1rem; margin-bottom: 0.75rem; cursor: move; box-shadow: 0 1px 3px rgba(0,0,0,0.1);';
          card.draggable = true;

          card.addEventListener('dragstart', (e) => {
            e.dataTransfer.setData('text/plain', lead.id);
            e.dataTransfer.effectAllowed = 'move';
          });

          const isConverted = lead.convertedToCustomer;

          card.innerHTML = `
            <div style="font-weight: 600; margin-bottom: 0.5rem; ${isConverted ? 'color: var(--ok);' : ''}">${escapeHtml(lead.name)}</div>
            <div style="display: flex; flex-wrap: wrap; gap: 0.25rem; margin-bottom: 0.5rem;">
              ${lead.city ? `<span style="font-size: 0.75rem; padding: 0.125rem 0.375rem; background: var(--bg); border-radius: 4px;">📍 ${escapeHtml(lead.city)}</span>` : ''}
              ${lead.mobile ? `<span style="font-size: 0.75rem; padding: 0.125rem 0.375rem; background: var(--bg); border-radius: 4px;">☎️ ${escapeHtml(lead.mobile)}</span>` : ''}
              ${lead.occasion ? `<span style="font-size: 0.75rem; padding: 0.125rem 0.375rem; background: var(--bg); border-radius: 4px;">🎯 ${escapeHtml(lead.occasion)}</span>` : ''}
              ${isConverted ? `<span style="font-size: 0.75rem; padding: 0.125rem 0.375rem; background: var(--ok); color: white; border-radius: 4px;">✓ Customer</span>` : ''}
            </div>
            <div style="display: flex; gap: 0.25rem; margin-top: 0.5rem;">
              ${lead.mobile ? `<a href="tel:${lead.mobile}" style="font-size: 0.75rem; color: var(--pri); text-decoration: none;">Call</a>` : ''}
              ${lead.mobile ? `<a href="https://wa.me/${lead.mobile.replace(/[^\d+]/g, '')}" target="_blank" style="font-size: 0.75rem; color: var(--pri); text-decoration: none;">WhatsApp</a>` : ''}
              ${lead.instagram ? `<a href="${this.getInstagramUrl(lead.instagram)}" target="_blank" style="font-size: 0.75rem; color: var(--pri); text-decoration: none;">Instagram</a>` : ''}
            </div>
            <div style="display: flex; gap: 0.25rem; margin-top: 0.75rem;">
              <button style="font-size: 0.75rem; padding: 0.25rem 0.5rem; border: 1px solid var(--bd); background: white; border-radius: 6px; cursor: pointer;" onclick="UnifiedApp.modules.leads.editLead('${lead.id}')">Edit</button>
              ${!isConverted ? `<button style="font-size: 0.75rem; padding: 0.25rem 0.5rem; border: 1px solid var(--ok); background: var(--ok); color: white; border-radius: 6px; cursor: pointer;" onclick="UnifiedApp.modules.leads.convertLeadToCustomer('${lead.id}')">Convert</button>` : ''}
              <button style="font-size: 0.75rem; padding: 0.25rem 0.5rem; border: 1px solid var(--err); background: white; color: var(--err); border-radius: 6px; cursor: pointer;" onclick="UnifiedApp.modules.leads.deleteLead('${lead.id}')">Delete</button>
            </div>
          `;

          return card;
        },

        getInstagramUrl(instagram) {
          if (!instagram) return '#';
          if (instagram.startsWith('http')) return instagram;
          if (instagram.startsWith('@')) instagram = instagram.slice(1);
          return `https://instagram.com/${instagram}`;
        },

        getStageColor(stage) {
          const colors = {
            'New': '#2563eb',
            'Contacted': '#0ea5e9',
            'Quote Sent': '#f59e0b',
            'Follow-Up': '#f97316',
            'Booked': '#22c55e',
            'Lost': '#ef4444'
          };
          return colors[stage] || '#2563eb';
        },

        editLead(id) {
          const lead = window.UnifiedApp.leads.find(l => l.id === id);
          if (lead) {
            this.showLeadModal(lead);
          }
        }
      };

      // Invoice Manager
      const InvoiceManager = {
        nextNumber: 1003690,

        init() {
          this.loadInvoices();
          this.bindEvents();
          this.render();
        },

        loadInvoices() {
          window.UnifiedApp.invoices = loadData('invoices');
          // Load next number from localStorage
          const savedNext = localStorage.getItem('invoice_next_number');
          if (savedNext) {
            this.nextNumber = parseInt(savedNext, 10);
          }
        },

        saveInvoices() {
          saveData('invoices', window.UnifiedApp.invoices);
          localStorage.setItem('invoice_next_number', this.nextNumber.toString());
        },

        bindEvents() {
          const newInvoiceBtn = document.getElementById('new-invoice-btn');
          const newQuoteBtn = document.getElementById('new-quote-btn');
          const typeFilter = document.getElementById('invoice-type-filter');
          const searchInput = document.getElementById('invoice-search');
          const currencyInput = document.getElementById('invoice-currency');

          if (newInvoiceBtn) {
            newInvoiceBtn.addEventListener('click', () => this.showInvoiceModal('Invoice'));
          }

          if (newQuoteBtn) {
            newQuoteBtn.addEventListener('click', () => this.showInvoiceModal('Quote'));
          }

          if (typeFilter) {
            typeFilter.addEventListener('change', () => this.render());
          }

          if (searchInput) {
            searchInput.addEventListener('input', () => this.render());
          }

          if (currencyInput) {
            currencyInput.addEventListener('input', () => this.render());
          }

          this.initInvoiceModal();
        },

        initInvoiceModal() {
          const modal = document.getElementById('invoice-modal');
          const form = document.getElementById('invoice-form');
          const closeBtn = document.getElementById('close-invoice-modal');
          const cancelBtn = document.getElementById('cancel-invoice-btn');
          const addItemBtn = document.getElementById('add-invoice-item-btn');
          const addCustomerBtn = document.getElementById('invoice-add-customer-btn');
          const applyTaxCheckbox = document.getElementById('invoice-apply-tax');
          const taxRateInput = document.getElementById('invoice-tax-rate');

          if (form) {
            form.addEventListener('submit', (e) => {
              e.preventDefault();
              this.handleInvoiceFormSubmit();
            });
          }

          if (closeBtn) {
            closeBtn.addEventListener('click', () => this.hideInvoiceModal());
          }

          if (cancelBtn) {
            cancelBtn.addEventListener('click', () => this.hideInvoiceModal());
          }

          if (addItemBtn) {
            addItemBtn.addEventListener('click', () => this.addInvoiceItem());
          }

          if (addCustomerBtn) {
            addCustomerBtn.addEventListener('click', () => {
              window.UnifiedApp.modules.customers.showCustomerModal();
            });
          }

          if (applyTaxCheckbox) {
            applyTaxCheckbox.addEventListener('change', () => this.calculateInvoiceTotals());
          }

          if (taxRateInput) {
            taxRateInput.addEventListener('input', () => this.calculateInvoiceTotals());
          }

          if (modal) {
            modal.addEventListener('click', (e) => {
              if (e.target === modal) {
                this.hideInvoiceModal();
              }
            });
          }
        },

        showInvoiceModal(type = 'Invoice', invoice = null) {
          const modal = document.getElementById('invoice-modal');
          const title = document.getElementById('invoice-modal-title');
          const form = document.getElementById('invoice-form');
          const dueLabel = document.getElementById('invoice-due-label');

          if (!modal || !form) return;

          // Reset form
          form.reset();

          // Set defaults
          document.getElementById('invoice-type').value = type;
          document.getElementById('invoice-number').value = this.nextNumber.toString();
          document.getElementById('invoice-currency').value = '€';
          document.getElementById('invoice-issue-date').valueAsDate = new Date();
          document.getElementById('invoice-due-date').valueAsDate = this.addDays(new Date(), 14);
          document.getElementById('invoice-tax-rate').value = '21';
          document.getElementById('invoice-apply-tax').checked = true;
          document.getElementById('invoice-notes').value = 'Thank you for your business.';
          document.getElementById('invoice-terms').value = 'Payment due within 14 days.';

          // Update labels based on type
          if (dueLabel) {
            dueLabel.textContent = type === 'Quote' ? 'Valid Until' : 'Due Date';
          }

          if (invoice) {
            // Edit mode
            title.textContent = `Edit ${invoice.type}`;
            // Populate form with invoice data
            // ... (implementation for edit mode)
            form.dataset.invoiceId = invoice.id;
          } else {
            // Add mode
            title.textContent = `New ${type}`;
            delete form.dataset.invoiceId;
          }

          this.updateInvoiceCustomerSelect();
          this.clearInvoiceItems();
          this.addInvoiceItem(); // Add one default item

          modal.classList.add('active');
          document.getElementById('invoice-customer').focus();
        },

        hideInvoiceModal() {
          const modal = document.getElementById('invoice-modal');
          if (modal) {
            modal.classList.remove('active');
          }
        },

        updateInvoiceCustomerSelect() {
          const select = document.getElementById('invoice-customer');
          if (!select) return;

          const customers = window.UnifiedApp.customers || [];
          select.innerHTML = '<option value="">Select Customer</option>' +
            customers.map(customer =>
              `<option value="${customer.id}">${escapeHtml(customer.name)}</option>`
            ).join('');
        },

        clearInvoiceItems() {
          const tbody = document.getElementById('invoice-items-body');
          if (tbody) {
            tbody.innerHTML = '';
          }
        },

        addInvoiceItem() {
          const tbody = document.getElementById('invoice-items-body');
          if (!tbody) return;

          const itemId = generateId();
          const row = document.createElement('tr');
          row.dataset.itemId = itemId;

          row.innerHTML = `
            <td><input type="text" placeholder="Service description" class="item-description" required></td>
            <td><input type="number" value="1" min="1" step="1" class="item-quantity" style="text-align: right;" required></td>
            <td><input type="number" value="0" min="0" step="0.01" class="item-price" style="text-align: right;" required></td>
            <td style="text-align: right; font-family: var(--font-mono);" class="item-total">€0.00</td>
            <td style="text-align: center;">
              <button type="button" class="btn" style="padding: 0.25rem 0.5rem; font-size: 0.75rem;" onclick="this.closest('tr').remove(); UnifiedApp.modules.invoices.calculateInvoiceTotals();">×</button>
            </td>
          `;

          tbody.appendChild(row);

          // Add event listeners for calculation
          const quantityInput = row.querySelector('.item-quantity');
          const priceInput = row.querySelector('.item-price');

          [quantityInput, priceInput].forEach(input => {
            input.addEventListener('input', () => {
              this.updateItemTotal(row);
              this.calculateInvoiceTotals();
            });
          });

          this.calculateInvoiceTotals();
        },

        updateItemTotal(row) {
          const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
          const price = parseFloat(row.querySelector('.item-price').value) || 0;
          const total = quantity * price;

          const totalCell = row.querySelector('.item-total');
          const currency = document.getElementById('invoice-currency').value || '€';
          totalCell.textContent = `${currency} ${total.toFixed(2)}`;
        },

        calculateInvoiceTotals() {
          const currency = document.getElementById('invoice-currency').value || '€';
          const applyTax = document.getElementById('invoice-apply-tax').checked;
          const taxRate = parseFloat(document.getElementById('invoice-tax-rate').value) || 0;

          let subtotal = 0;
          const itemRows = document.querySelectorAll('#invoice-items-body tr');

          itemRows.forEach(row => {
            const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
            const price = parseFloat(row.querySelector('.item-price').value) || 0;
            subtotal += quantity * price;
            this.updateItemTotal(row);
          });

          const tax = applyTax ? (subtotal * taxRate / 100) : 0;
          const total = subtotal + tax;

          document.getElementById('invoice-subtotal').textContent = `${currency} ${subtotal.toFixed(2)}`;
          document.getElementById('invoice-tax').textContent = `${currency} ${tax.toFixed(2)}`;
          document.getElementById('invoice-total').textContent = `${currency} ${total.toFixed(2)}`;
        },

        handleInvoiceFormSubmit() {
          const form = document.getElementById('invoice-form');
          const invoiceId = form.dataset.invoiceId;

          const customerId = document.getElementById('invoice-customer').value;
          if (!customerId) {
            alert('Please select a customer.');
            return;
          }

          const customer = window.UnifiedApp.customers.find(c => c.id === customerId);
          if (!customer) {
            alert('Selected customer not found.');
            return;
          }

          // Get items
          const items = [];
          const itemRows = document.querySelectorAll('#invoice-items-body tr');

          itemRows.forEach(row => {
            const description = row.querySelector('.item-description').value.trim();
            const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
            const price = parseFloat(row.querySelector('.item-price').value) || 0;

            if (description && quantity > 0 && price >= 0) {
              items.push({
                id: generateId(),
                description,
                quantity,
                price
              });
            }
          });

          if (items.length === 0) {
            alert('Please add at least one item.');
            return;
          }

          const invoiceData = {
            type: document.getElementById('invoice-type').value,
            number: document.getElementById('invoice-number').value,
            currency: document.getElementById('invoice-currency').value,
            issueDate: document.getElementById('invoice-issue-date').value,
            dueDate: document.getElementById('invoice-due-date').value,
            taxRate: parseFloat(document.getElementById('invoice-tax-rate').value) || 0,
            applyTax: document.getElementById('invoice-apply-tax').checked,
            customerId: customer.id,
            customerName: customer.name,
            customerEmail: customer.email || '',
            customerPhone: customer.phone || '',
            items: items,
            notes: document.getElementById('invoice-notes').value.trim(),
            terms: document.getElementById('invoice-terms').value.trim()
          };

          if (invoiceId) {
            // Update existing invoice
            // ... (implementation for edit mode)
          } else {
            // Add new invoice
            const invoice = {
              id: generateId(),
              ...invoiceData,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            };

            // Calculate totals
            this.calculateTotals(invoice);

            window.UnifiedApp.invoices.push(invoice);
            this.nextNumber++;
            this.saveInvoices();
            this.render();
          }

          this.hideInvoiceModal();
        },

        createInvoiceForCustomer(customer) {
          this.showInvoiceModal('Invoice');

          // Pre-select the customer after a short delay to ensure modal is open
          setTimeout(() => {
            const customerSelect = document.getElementById('invoice-customer');
            if (customerSelect) {
              customerSelect.value = customer.id;
            }
          }, 100);
        },

        addDays(date, days) {
          const result = new Date(date);
          result.setDate(result.getDate() + days);
          return result;
        },

        calculateTotals(invoice) {
          const subtotal = invoice.items.reduce((sum, item) =>
            sum + (item.quantity * item.price), 0
          );

          const tax = invoice.applyTax ? (subtotal * invoice.taxRate / 100) : 0;
          const total = subtotal + tax;

          invoice.subtotal = subtotal;
          invoice.tax = tax;
          invoice.total = total;

          return { subtotal, tax, total };
        },

        deleteInvoice(id) {
          if (confirm('Delete this document?')) {
            window.UnifiedApp.invoices = window.UnifiedApp.invoices.filter(i => i.id !== id);
            this.saveInvoices();
            this.render();
          }
        },

        getFilteredInvoices() {
          const typeFilter = document.getElementById('invoice-type-filter')?.value || '';
          const searchTerm = document.getElementById('invoice-search')?.value.toLowerCase() || '';

          return window.UnifiedApp.invoices.filter(invoice => {
            const matchesType = !typeFilter || invoice.type === typeFilter;
            const matchesSearch = !searchTerm ||
              invoice.number.toLowerCase().includes(searchTerm) ||
              (invoice.customerName && invoice.customerName.toLowerCase().includes(searchTerm));

            return matchesType && matchesSearch;
          });
        },

        render() {
          this.updateStats();
          this.renderInvoiceTable();
        },

        updateStats() {
          const filtered = this.getFilteredInvoices();
          const currency = document.getElementById('invoice-currency')?.value || '€';

          const totals = filtered.reduce((acc, invoice) => {
            acc.subtotal += invoice.subtotal || 0;
            acc.tax += invoice.tax || 0;
            acc.total += invoice.total || 0;
            return acc;
          }, { subtotal: 0, tax: 0, total: 0 });

          const subtotalEl = document.getElementById('invoice-sum-subtotal');
          const taxEl = document.getElementById('invoice-sum-tax');
          const totalEl = document.getElementById('invoice-sum-total');

          if (subtotalEl) subtotalEl.textContent = `${currency} ${totals.subtotal.toFixed(2)}`;
          if (taxEl) taxEl.textContent = `${currency} ${totals.tax.toFixed(2)}`;
          if (totalEl) totalEl.textContent = `${currency} ${totals.total.toFixed(2)}`;
        },

        renderInvoiceTable() {
          const tbody = document.getElementById('invoice-table-body');
          if (!tbody) return;

          const invoices = this.getFilteredInvoices().sort((a, b) =>
            new Date(b.createdAt) - new Date(a.createdAt)
          );
          const currency = document.getElementById('invoice-currency')?.value || '€';

          tbody.innerHTML = invoices.map(invoice => `
            <tr style="background: var(--card); border: 1px solid var(--bd); border-radius: 12px;">
              <td style="padding: 12px; border-top-left-radius: 12px; border-bottom-left-radius: 12px;">
                <div style="font-weight: 600;">${escapeHtml(invoice.number)}</div>
              </td>
              <td style="padding: 12px;">
                <span style="padding: 0.25rem 0.5rem; border-radius: 6px; font-size: 0.75rem; font-weight: 600; background: ${invoice.type === 'Invoice' ? 'var(--pri)' : 'var(--warn)'}; color: white;">
                  ${invoice.type}
                </span>
              </td>
              <td style="padding: 12px;">${invoice.issueDate}</td>
              <td style="padding: 12px;">${invoice.dueDate}</td>
              <td style="padding: 12px;">${escapeHtml(invoice.customerName || '')}</td>
              <td style="padding: 12px; text-align: right; font-family: var(--font-mono);">
                ${currency} ${(invoice.subtotal || 0).toFixed(2)}
              </td>
              <td style="padding: 12px; text-align: right; font-family: var(--font-mono);">
                ${currency} ${(invoice.tax || 0).toFixed(2)}
              </td>
              <td style="padding: 12px; text-align: right; font-family: var(--font-mono);">
                ${currency} ${(invoice.total || 0).toFixed(2)}
              </td>
              <td style="padding: 12px; text-align: center; border-top-right-radius: 12px; border-bottom-right-radius: 12px;">
                <button class="btn" style="padding: 0.25rem 0.5rem; font-size: 0.75rem;" onclick="UnifiedApp.modules.invoices.deleteInvoice('${invoice.id}')">
                  Delete
                </button>
              </td>
            </tr>
          `).join('');
        }
      };

      // Register modules
      window.UnifiedApp.modules.customers = CustomerManager;
      window.UnifiedApp.modules.accounting = AccountingManager;
      window.UnifiedApp.modules.leads = LeadManager;
      window.UnifiedApp.modules.invoices = InvoiceManager;

      // Navigation system
      function initNavigation() {
        const navTabs = document.querySelectorAll('.nav-tab');
        const modules = document.querySelectorAll('.module');

        navTabs.forEach(tab => {
          tab.addEventListener('click', () => {
            const moduleId = tab.dataset.module;
            switchModule(moduleId);
          });
        });

        function switchModule(moduleId) {
          // Update navigation
          navTabs.forEach(tab => {
            tab.classList.toggle('active', tab.dataset.module === moduleId);
          });

          // Update modules
          modules.forEach(module => {
            module.classList.toggle('active', module.id === `${moduleId}-module`);
          });

          window.UnifiedApp.currentModule = moduleId;

          // Trigger module-specific initialization
          if (window.UnifiedApp.modules && window.UnifiedApp.modules[moduleId]) {
            window.UnifiedApp.modules[moduleId].init();
          }

          // Update customer selects when switching modules
          if (moduleId === 'accounting' && window.UnifiedApp.modules.accounting) {
            window.UnifiedApp.modules.accounting.updateCustomerSelect();
            window.UnifiedApp.modules.accounting.updateCategoriesDatalist();
          }

          // Update leads module when switching
          if (moduleId === 'leads' && window.UnifiedApp.modules.leads) {
            window.UnifiedApp.modules.leads.render();
          }

          // Update invoices module when switching
          if (moduleId === 'invoices' && window.UnifiedApp.modules.invoices) {
            window.UnifiedApp.modules.invoices.render();
          }
        }
      }

      // Export/Import functionality
      function initExportImport() {
        const exportBtn = document.getElementById('export-all-btn');
        const importBtn = document.getElementById('import-all-btn');
        const importFile = document.getElementById('import-file');

        if (exportBtn) {
          exportBtn.addEventListener('click', () => {
            const data = {
              customers: window.UnifiedApp.customers,
              leads: window.UnifiedApp.leads,
              entries: window.UnifiedApp.entries,
              invoices: window.UnifiedApp.invoices,
              exportDate: new Date().toISOString()
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `unified-business-data-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
          });
        }

        if (importBtn) {
          importBtn.addEventListener('click', () => {
            importFile.click();
          });
        }

        if (importFile) {
          importFile.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = (event) => {
              try {
                const data = JSON.parse(event.target.result);

                if (confirm('This will replace all current data. Continue?')) {
                  window.UnifiedApp.customers = data.customers || [];
                  window.UnifiedApp.leads = data.leads || [];
                  window.UnifiedApp.entries = data.entries || [];
                  window.UnifiedApp.invoices = data.invoices || [];

                  // Save to localStorage
                  saveData('customers', window.UnifiedApp.customers);
                  saveData('leads', window.UnifiedApp.leads);
                  saveData('entries', window.UnifiedApp.entries);
                  saveData('invoices', window.UnifiedApp.invoices);

                  // Refresh all modules
                  window.UnifiedApp.refreshAll();
                  updateHeaderCounts();

                  alert('Data imported successfully!');
                }
              } catch (error) {
                alert('Error importing data: ' + error.message);
              }
            };
            reader.readAsText(file);
            e.target.value = '';
          });
        }
      }

      window.updateHeaderCounts = function() {
        const customerCount = document.getElementById('header-customer-count');
        const leadCount = document.getElementById('header-lead-count');

        if (customerCount) customerCount.textContent = window.UnifiedApp.customers.length;
        if (leadCount) leadCount.textContent = window.UnifiedApp.leads.length;
      };

      // Initialize the application
      function init() {
        initNavigation();
        initExportImport();

        // Initialize modules

        if (window.UnifiedApp.modules.customers) {
          window.UnifiedApp.modules.customers.init();
        }

        if (window.UnifiedApp.modules.accounting) {
          window.UnifiedApp.modules.accounting.init();
        }

        if (window.UnifiedApp.modules.leads) {
          window.UnifiedApp.modules.leads.init();
        }

        if (window.UnifiedApp.modules.invoices) {
          window.UnifiedApp.modules.invoices.init();
        }

        // Add sample data if no data exists
        addSampleDataIfEmpty();

        // Update header counts
        updateHeaderCounts();

        // Add global keyboard shortcuts
        document.addEventListener('keydown', (e) => {
          if (e.key === 'Escape') {
            // Close any open modals
            const openModals = document.querySelectorAll('.modal.active');
            openModals.forEach(modal => modal.classList.remove('active'));
          }
        });

        console.log('Unified Business Management System initialized');
      }

      function addSampleDataIfEmpty() {
        // Only add sample data if no data exists
        if (window.UnifiedApp.customers.length === 0 &&
            window.UnifiedApp.leads.length === 0 &&
            window.UnifiedApp.entries.length === 0) {

          // Add sample customers
          const sampleCustomers = [
            {
              name: 'Alice Johnson',
              email: '<EMAIL>',
              phone: '+**************',
              city: 'Brussels',
              country: 'Belgium',
              source: 'manual'
            },
            {
              name: 'Bob Smith Photography',
              email: '<EMAIL>',
              phone: '+**************',
              city: 'Antwerp',
              country: 'Belgium',
              source: 'lead'
            }
          ];

          sampleCustomers.forEach(customerData => {
            window.UnifiedApp.modules.customers.addCustomer(customerData);
          });

          // Add sample leads
          const sampleLeads = [
            {
              name: 'Emma Wilson',
              mobile: '+32 468 345 678',
              email: '<EMAIL>',
              city: 'Ghent',
              country: 'Belgium',
              occasion: 'Wedding',
              services: ['Wedding Photography', 'Wedding Videography'],
              stage: 'New'
            },
            {
              name: 'David Brown',
              mobile: '+32 468 901 234',
              email: '<EMAIL>',
              city: 'Bruges',
              country: 'Belgium',
              occasion: 'Corporate Event',
              services: ['Event Videography'],
              stage: 'Contacted'
            }
          ];

          sampleLeads.forEach(leadData => {
            window.UnifiedApp.modules.leads.addLead(leadData);
          });

          // Add sample accounting entries
          const customers = window.UnifiedApp.customers;
          if (customers.length > 0) {
            const sampleEntries = [
              {
                date: new Date().toISOString().split('T')[0],
                type: 'income',
                category: 'Wedding Photography',
                description: 'Wedding shoot for Alice Johnson',
                amount: 1500,
                customerId: customers[0].id
              },
              {
                date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                type: 'expense',
                category: 'Equipment',
                description: 'Camera lens rental',
                amount: 200
              }
            ];

            sampleEntries.forEach(entryData => {
              window.UnifiedApp.modules.accounting.addEntryFromData(entryData);
            });
          }

          console.log('Sample data added to demonstrate the unified system');
        }
      }

      // Start the application when DOM is ready
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
      } else {
        init();
      }
    })();
  </script>
</body>
</html>
