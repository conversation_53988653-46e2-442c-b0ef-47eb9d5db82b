<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Invoice & Quote — ver2 (A4, Dashboard, Plain Numbers + Edit/Delete)</title>
  <style>
    :root{ --bg:#f7f7f8; --panel:#fff; --ink:#0f172a; --muted:#6b7280; --pri:#2563eb; --bd:#e5e7eb; }
    *{box-sizing:border-box}
    html,body{height:100%}
    body{margin:0;background:var(--bg);color:var(--ink);font-family:ui-sans-serif,system-ui,-apple-system,Segoe UI,Roboto,Arial,"Noto Sans";line-height:1.35}
    h1,h2,h3{margin:0 0 .5rem}
    .wrap{max-width:1200px;margin:0 auto;padding:16px}
    header{display:flex;align-items:center;justify-content:space-between;gap:12px;margin-bottom:8px}
    .brand{display:flex;align-items:center;gap:12px}
    .tag{font-size:.9rem;color:var(--muted)}
    .actions{display:flex;flex-wrap:wrap;gap:8px}
    button{border:1px solid var(--bd);background:#fff;color:var(--ink);padding:8px 12px;border-radius:10px;cursor:pointer;font-weight:600}
    button.primary{background:var(--pri);color:#fff;border-color:var(--pri)}
    button.ghost{background:transparent}
    button.danger{background:#fff0f0;border-color:#ffdede;color:#b91c1c}
    input,select,textarea{width:100%;padding:8px 10px;border:1px solid var(--bd);border-radius:10px;background:#fff;color:var(--ink)}
    label{font-size:.85rem;color:var(--muted)}
    .grid{display:grid;grid-template-columns:1fr 1fr;gap:14px}
    .panel{background:var(--panel);border:1px solid var(--bd);border-radius:16px;padding:14px;box-shadow:0 1px 0 rgba(0,0,0,.02),0 2px 16px rgba(0,0,0,.04)}
    .section{display:grid;gap:8px;margin:6px 0 14px}
    .row{display:grid;grid-template-columns:1fr 1fr;gap:10px}
    .row-4{display:grid;grid-template-columns:repeat(4,1fr);gap:10px}
    .table{width:100%;border-collapse:collapse}
    .table th,.table td{border-bottom:1px solid var(--bd);padding:8px;text-align:left;vertical-align:top}
    .right{text-align:right}.center{text-align:center}.muted{color:var(--muted)}
    .logo-preview{width:72px;height:72px;border:1px dashed var(--bd);border-radius:12px;display:flex;align-items:center;justify-content:center;overflow:hidden;background:#fff}
    .small{font-size:.8rem}
    .preview .print-container{max-width:210mm;margin:0 auto}
    .doc{background:#fff;border:1px solid var(--bd);border-radius:12px;overflow:hidden}
    .doc .head{display:flex;justify-content:space-between;padding:20px;border-bottom:1px solid var(--bd)}
    .doc .grid-2{display:grid;grid-template-columns:1fr 1fr;gap:16px;padding:0 20px 14px}
    .doc .boxed{border:1px solid var(--bd);border-radius:10px;padding:12px}
    .doc table{width:100%;border-collapse:collapse}
    .doc th,.doc td{padding:10px;border-bottom:1px solid var(--bd);font-size:.95rem}
    .doc tfoot td{border-bottom:none}
    .doc .footer{display:flex;justify-content:space-between;gap:16px;padding:16px 20px 22px;border-top:1px solid var(--bd)}
    .doc-title{font-weight:900;font-size:1.6rem;letter-spacing:.04em}
    .doc-num{font-weight:800;font-size:1rem}
    .hidden{display:none !important}
    .tabs{display:flex;gap:8px;margin:8px 0 12px}
    .tabs button{border-radius:999px;padding:8px 14px}
    .tabs button.active{background:var(--pri);border-color:var(--pri);color:#fff}
    .cards{display:grid;grid-template-columns:repeat(3,1fr);gap:12px;margin-bottom:12px}
    .card{background:#fff;border:1px solid var(--bd);border-radius:14px;padding:12px}
    .card .k{font-size:.8rem;color:var(--muted)}
    .card .v{font-size:1.25rem;font-weight:800}
    @media print{
      @page{size:A4;margin:12mm}
      header,.panel.left,.no-print,.tabs,#dashboardPanel{display:none !important}
      body{background:#fff;-webkit-print-color-adjust:exact;print-color-adjust:exact}
      .wrap{max-width:none;padding:0;margin:0}
      .grid{grid-template-columns:1fr !important;gap:0 !important}
      .panel.preview{border:none;box-shadow:none;padding:0;margin:0}
      .preview .print-container{max-width:none;padding:0;margin:0}
      .doc{width:100%;border:none;border-radius:0}
      .doc .head,.doc .grid-2,.doc .footer{padding-left:0;padding-right:0}
      .doc th,.doc td{font-size:11pt}
      .doc .doc-title{font-size:20pt}.doc .doc-num{font-size:12pt}
    }
  </style>
</head>
<body>
  <div class="wrap">
    <header>
      <div class="brand">
        <div class="logo-preview" id="logoPreview"><span class="muted small">Logo</span></div>
        <div>
          <h1>Invoice & Quote — ver2 (A4)</h1>
          <div class="tag">Offline • Single-file • LocalStorage • A4 Print • Dashboard • Plain Numbers</div>
        </div>
      </div>
      <div class="actions no-print">
        <button class="ghost" id="btnNew">New</button>
        <button id="btnSave">Save</button>
        <button id="btnLoad">Load</button>
        <button id="btnExport">Export JSON</button>
        <button id="btnImport">Import JSON</button>
        <button class="primary" id="btnPrint">Print / Save PDF</button>
      </div>
    </header>

    <div class="tabs no-print">
      <button id="tabEditor" class="active">Editor</button>
      <button id="tabDashboard">Dashboard</button>
    </div>

    <!-- DASHBOARD -->
    <div id="dashboardPanel" class="panel hidden">
      <div class="section">
        <div class="row-4">
          <div><label>Type</label>
            <select id="dash_type">
              <option value="ALL">All (Invoices & Quotes)</option>
              <option value="Invoice">Invoices only</option>
              <option value="Quote">Quotes only</option>
            </select>
          </div>
          <div><label>Search</label><input id="dash_search" placeholder="Number or Customer"/></div>
          <div><label>Currency (display)</label><input id="dash_currency" value="€"/></div>
          <div style="display:flex;align-items:flex-end;gap:8px">
            <button id="dash_refresh">Refresh</button>
            <button id="dash_open_selected" class="primary" disabled>Open Selected</button>
            <button id="dash_delete_selected" class="danger" disabled>Delete Selected</button>
          </div>
        </div>
      </div>

      <div class="cards">
        <div class="card"><div class="k">Net (Subtotal)</div><div class="v" id="sum_subtotal">€ 0.00</div></div>
        <div class="card"><div class="k">VAT (Tax)</div><div class="v" id="sum_tax">€ 0.00</div></div>
        <div class="card"><div class="k">Gross (Total)</div><div class="v" id="sum_total">€ 0.00</div></div>
      </div>

      <div class="section">
        <table class="table" id="dash_table">
          <thead>
            <tr>
              <th class="center"><input type="checkbox" id="dash_select_all"/></th>
              <th>Number</th>
              <th>Type</th>
              <th>Issue</th>
              <th>Due/Valid</th>
              <th>Customer</th>
              <th class="right">Net</th>
              <th class="right">VAT</th>
              <th class="right">Gross</th>
              <th class="right">Actions</th>
            </tr>
          </thead>
          <tbody id="dash_body"></tbody>
        </table>
      </div>
    </div>

    <!-- EDITOR -->
    <div id="editorPanel">
      <div class="grid">
        <!-- LEFT: FORM -->
        <div class="panel left">
          <div class="section">
            <div style="display:flex;justify-content:space-between;align-items:center">
              <h2>Business</h2>
              <span class="small muted">Business fields are locked. <button class="ghost" id="unlockBiz">Unlock</button></span>
            </div>
            <div class="row-4" style="grid-template-columns:repeat(3,1fr) 1fr">
              <!-- CLEARED DEFAULTS -->
              <div><label>Business Name</label><input id="biz_name" placeholder="Your business name" value="" disabled /></div>
              <div><label>Email</label><input id="biz_email" placeholder="<EMAIL>" value="" disabled /></div>
              <div><label>Phone</label><input id="biz_phone" placeholder="+32 ..." value="" disabled /></div>
            </div>
            <div class="row">
              <div><label>Address</label><input id="biz_address" placeholder="Street, City, Country" value="" disabled /></div>
              <div><label>VAT / Company No.</label><input id="biz_vat" placeholder="BE0XXXXXXXXX" value="" disabled /></div>
            </div>
            <div class="row">
              <div><label>IBAN (optional)</label><input id="biz_iban" placeholder="BE________________" value="" disabled /></div>
              <div><label>Logo (remembered after first upload)</label><input id="biz_logo" type="file" accept="image/*" /></div>
            </div>
          </div>

          <div class="section">
            <h2>Customer</h2>
            <div class="row">
              <div><label>Customer Name / Company</label><input id="cus_name" placeholder="Name or Company" /></div>
              <div><label>Email</label><input id="cus_email" placeholder="<EMAIL>" /></div>
            </div>
            <div class="row">
              <div><label>Phone</label><input id="cus_phone" placeholder="+32 ..." /></div>
              <div><label>Address</label><input id="cus_address" placeholder="Street, City, Country" /></div>
            </div>
            <div class="row">
              <div><label>VAT number</label><input id="cus_vat" placeholder="BE0123456789" /></div>
              <div></div>
            </div>
          </div>

          <div class="section">
            <h2>Document</h2>
            <div class="row-4" style="grid-template-columns:repeat(3,1fr)">
              <div><label>Type</label><select id="doc_type"><option>Invoice</option><option>Quote</option></select></div>
              <div><label>Number</label><input id="doc_number" placeholder="1003690" /></div>
              <div><label>Currency</label><input id="doc_currency" value="€" /></div>
            </div>
            <div class="row-4" style="grid-template-columns:repeat(3,1fr)">
              <div><label>Issue Date</label><input id="doc_issue" type="date" /></div>
              <div><label id="due_label">Due Date</label><input id="doc_due" type="date" /></div>
              <div><label>Tax %</label><input id="tax_rate" type="number" min="0" step="0.01" value="21" /></div>
            </div>
            <div class="row">
              <div class="small"><input id="apply_tax" type="checkbox" checked /> Apply tax</div>
            </div>
          </div>

          <div class="section">
            <div style="display:flex;align-items:center;justify-content:space-between">
              <h2>Items</h2>
              <div>
                <button id="btnAddItem" class="ghost">+ Add Row</button>
                <button id="btnClearItems" class="danger">Clear</button>
              </div>
            </div>
            <table class="table" id="items_table">
              <thead>
                <tr>
                  <th style="width:40%">Description</th>
                  <th class="right">Qty</th>
                  <th class="right">Unit Price</th>
                  <th class="right">Line Total</th>
                  <th class="center">—</th>
                </tr>
              </thead>
              <tbody id="items_body"></tbody>
            </table>
          </div>

          <div class="section">
            <div class="row">
              <div><label>Notes</label><textarea id="doc_notes" rows="3" placeholder="Thank you for your business."></textarea></div>
              <div><label>Terms</label><textarea id="doc_terms" rows="3" placeholder="Payment due within 14 days."></textarea></div>
            </div>
          </div>
        </div>

        <!-- RIGHT: PREVIEW -->
        <div class="panel preview">
          <div class="print-container">
            <div id="doc_preview" class="doc"><!-- injected --></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <input type="file" id="import_file" accept="application/json" style="display:none"/>

  <script>
  (function(){
    const $ = s=>document.querySelector(s);

    // ---------- Plain numeric sequence (starts at 1003690) ----------
    const SEQ_KEY = "seq_plain_next";
    const SEQ_START = 1003690;
    function nextNumberPlain(){
      let next = parseInt(localStorage.getItem(SEQ_KEY) || String(SEQ_START), 10);
      const num = String(next);
      localStorage.setItem(SEQ_KEY, String(next + 1));
      return num;
    }

    // CLEARED DEFAULT BUSINESS INFO
    const state = {
      business:{
        name:"",
        email:"",
        phone:"",
        address:"",
        vat:"",
        iban:"",
        logo:""
      },
      customer:{name:"",email:"",phone:"",address:"", vat:""},
      doc:{type:"Invoice",number:"",currency:"€",issue:"",due:"",notes:"",terms:""},
      settings:{taxRate:21,applyTax:true},
      items:[]
    };

    const todayStr=()=>{const d=new Date();const m=String(d.getMonth()+1).padStart(2,"0");const day=String(d.getDate()).padStart(2,"0");return d.getFullYear()+"-"+m+"-"+day;}
    const addDays=(ds,n)=>{const d=new Date(ds||todayStr());d.setDate(d.getDate()+n);const m=String(d.getMonth()+1).padStart(2,"0");const day=String(d.getDate()).padStart(2,"0");return d.getFullYear()+"-"+m+"-"+day;}
    const parseNum=v=>{const n=parseFloat(v);return isFinite(n)?n:0;}
    const money=(n,sym)=>{ const s = sym || $("#doc_currency").value || state.doc.currency || "€"; const v=isFinite(n)?n:0; return s+" "+v.toFixed(2); }

    // calc
    let calcScheduled=false;
    function calc(){
      if(calcScheduled) return;
      calcScheduled=true;
      requestAnimationFrame(()=>{
        const subtotal=state.items.reduce((s,it)=>s+(parseNum(it.qty)*parseNum(it.price)),0);
        const taxRate=parseNum($("#tax_rate").value);
        const tax=$("#apply_tax").checked? subtotal*(taxRate/100):0;
        const total=subtotal+tax;
        state._totals={subtotal,tax,total};
        renderPreview();
        calcScheduled=false;
      });
    }

    // Items
    function rowHTML(it,i){
      return `
        <tr data-row="${i}">
          <td><input data-k="desc" data-i="${i}" value="${(it.desc||"").replace(/"/g,'&quot;')}" placeholder="Service or product" /></td>
          <td class="right"><input data-k="qty" data-i="${i}" type="number" inputmode="decimal" step="0.01" min="0" value="${it.qty||0}" /></td>
          <td class="right"><input data-k="price" data-i="${i}" type="number" inputmode="decimal" step="0.01" min="0" value="${it.price||0}" /></td>
          <td class="right"><span class="money line-total">${money(parseNum(it.qty)*parseNum(it.price))}</span></td>
          <td class="center"><button data-del="${i}" class="danger">Delete</button></td>
        </tr>`;
    }
    function renderItems(){ $("#items_body").innerHTML = state.items.map((it,i)=>rowHTML(it,i)).join(""); }
    $("#items_body").addEventListener("input",(e)=>{
      const t=e.target; if(!t.matches("input[data-k]")) return;
      const i=parseInt(t.dataset.i,10), k=t.dataset.k;
      state.items[i][k]=(k==="desc")?t.value:parseNum(t.value);
      const tr=t.closest("tr");
      const qty=parseNum(tr.querySelector('input[data-k="qty"]').value);
      const price=parseNum(tr.querySelector('input[data-k="price"]').value);
      tr.querySelector(".line-total").textContent = money(qty*price);
      calc();
    });
    $("#items_body").addEventListener("click",(e)=>{
      const btn=e.target.closest("button[data-del]"); if(!btn) return;
      const idx=parseInt(btn.dataset.del,10);
      state.items.splice(idx,1); renderItems(); calc();
    });
    function addItem(desc="",qty=1,price=0){ state.items.push({desc,qty,price}); renderItems(); calc(); }

    // Logo remember
    function renderLogo(){
      const box=$("#logoPreview");
      box.innerHTML = state.business.logo ? `<img src="${state.business.logo}" style="max-width:100%;max-height:100%" alt="logo"/>` : `<span class="muted small">Logo</span>`;
    }
    const savedLogo = localStorage.getItem("biz_logo_dataurl");
    if(savedLogo){ state.business.logo = savedLogo; renderLogo(); }
    $("#biz_logo").addEventListener("change",(e)=>{
      const f=e.target.files[0]; if(!f) return;
      const r=new FileReader();
      r.onload = ev=>{
        state.business.logo = ev.target.result;
        localStorage.setItem("biz_logo_dataurl", state.business.logo);
        renderLogo(); renderPreview();
        alert("Logo saved. It will load automatically next time.");
      };
      r.readAsDataURL(f);
    });

    // Bindings
    [["cus_name","customer","name"],["cus_email","customer","email"],["cus_phone","customer","phone"],["cus_address","customer","address"],["cus_vat","customer","vat"],
     ["doc_currency","doc","currency"],["doc_number","doc","number"],["doc_notes","doc","notes"],["doc_terms","doc","terms"],
     ["tax_rate","settings","taxRate"]].forEach(([id,g,k])=>{
       const el=$("#"+id);
       el.addEventListener("input", ()=>{ state[g][k]=k==="taxRate"?parseNum(el.value):el.value; calc(); });
     });
    $("#apply_tax").addEventListener("change", ()=>{ state.settings.applyTax=$("#apply_tax").checked; calc(); });

    $("#unlockBiz").addEventListener("click", ()=>{
      if(!confirm("Unlock business fields for editing?")) return;
      ["biz_name","biz_email","biz_phone","biz_address","biz_vat","biz_iban"].forEach(id=>{ $("#"+id).disabled=false; });
    });

    // Type change (number only generated when empty)
    $("#doc_type").addEventListener("change", ()=>{
      state.doc.type=$("#doc_type").value;
      $("#due_label").textContent=(state.doc.type==="Quote"?"Valid Until":"Due Date");
      if(!state.doc.number){ state.doc.number = nextNumberPlain(); $("#doc_number").value = state.doc.number; }
      const issue=$("#doc_issue").value||todayStr();
      $("#doc_due").value = addDays(issue,14);
      calc();
    });
    $("#doc_issue").addEventListener("input", ()=>{ state.doc.issue=$("#doc_issue").value; if(!$("#doc_due").value){ $("#doc_due").value=addDays(state.doc.issue,14);} calc(); });
    $("#doc_due").addEventListener("input", ()=>{ state.doc.due=$("#doc_due").value; calc(); });

    // Save/Load helpers
    function docToJSON(){ return JSON.stringify(state); }
    function fromJSON(json){
      try{
        const obj=(typeof json==="string")?JSON.parse(json):json;
        ["business","customer","doc","settings"].forEach(k=> state[k]=Object.assign({}, state[k], obj[k]||{}));
        state.items=Array.isArray(obj.items)?obj.items:[];
        $("#biz_name").value=state.business.name||""; $("#biz_email").value=state.business.email||""; $("#biz_phone").value=state.business.phone||"";
        $("#biz_address").value=state.business.address||""; $("#biz_vat").value=state.business.vat||""; $("#biz_iban").value=state.business.iban||"";
        renderLogo();
        $("#cus_name").value=state.customer.name||""; $("#cus_email").value=state.customer.email||""; $("#cus_phone").value=state.customer.phone||""; $("#cus_address").value=state.customer.address||"";
        $("#cus_vat").value=state.customer.vat||"";
        $("#doc_type").value=state.doc.type||"Invoice"; $("#doc_number").value=state.doc.number||""; $("#doc_currency").value=state.doc.currency||"€";
        $("#doc_issue").value=state.doc.issue||todayStr(); $("#doc_due").value=state.doc.due||addDays($("#doc_issue").value,14);
        $("#doc_notes").value=state.doc.notes||""; $("#doc_terms").value=state.doc.terms||"";
        $("#tax_rate").value=state.settings.taxRate ?? 21; $("#apply_tax").checked=!!state.settings.applyTax;
        renderItems(); calc();
      }catch(e){ alert("Failed to load JSON: "+e.message); }
    }
    function saveLocal(){
      if(!state.doc.number){ alert("Please set a document number first."); return; }
      localStorage.setItem("doc::"+state.doc.number, docToJSON());
      const idxKey="doc_index"; const arr=JSON.parse(localStorage.getItem(idxKey)||"[]"); if(!arr.includes(state.doc.number)){arr.push(state.doc.number); localStorage.setItem(idxKey, JSON.stringify(arr));}
      alert("Saved: "+state.doc.number);
    }
    function listSavedNumbers(){
      const idxKey="doc_index";
      const index = JSON.parse(localStorage.getItem(idxKey) || "[]");
      for(const k in localStorage){
        if(k && k.startsWith && k.startsWith("doc::")){
          const num = k.slice(5);
          if(!index.includes(num)) index.push(num);
        }
      }
      return index;
    }
    function removeFromIndex(num){
      const idxKey="doc_index";
      const arr=JSON.parse(localStorage.getItem(idxKey)||"[]").filter(n=>n!==num);
      localStorage.setItem(idxKey, JSON.stringify(arr));
    }
    function deleteDoc(num){
      localStorage.removeItem("doc::"+num);
      removeFromIndex(num);
    }
    function loadLocal(){
      const arr=listSavedNumbers(); if(arr.length===0){ alert("No saved documents."); return; }
      const choice=prompt("Type a number to load:\n"+arr.map((n,i)=>`${i+1}. ${n}`).join("\n")); if(!choice) return;
      const num=arr[parseInt(choice,10)-1]; if(!num) return;
      const doc=localStorage.getItem("doc::"+num); if(!doc){ alert("Not found."); return; }
      fromJSON(doc); setTab("editor");
    }
    $("#btnSave").addEventListener("click", saveLocal);
    $("#btnLoad").addEventListener("click", loadLocal);
    $("#btnExport").addEventListener("click", ()=>{
      const suggested=(state.doc.number||"document")+".json";
      const name=prompt("File name for export:", suggested) || suggested;
      const blob=new Blob([docToJSON()],{type:"application/json"});
      const a=document.createElement("a"); a.href=URL.createObjectURL(blob); a.download=name; a.click(); URL.revokeObjectURL(a.href);
    });
    $("#btnImport").addEventListener("click", ()=> $("#import_file").click());
    $("#import_file").addEventListener("change", e=>{
      const f=e.target.files[0]; if(!f) return;
      const r=new FileReader(); r.onload=ev=>fromJSON(ev.target.result); r.readAsText(f); e.target.value="";
    });
    $("#btnPrint").addEventListener("click", ()=> window.print());
    $("#btnNew").addEventListener("click", ()=>{
      if(!confirm("Start a new document? Unsaved changes will be lost.")) return;
      const keepBiz=JSON.parse(JSON.stringify(state.business));
      const keepSettings=JSON.parse(JSON.stringify(state.settings));
      state.business=keepBiz; state.settings=keepSettings;
      state.customer={name:"",email:"",phone:"",address:"",vat:""};
      state.doc={type:$("#doc_type").value||"Invoice", number:"", currency:$("#doc_currency").value||"€", issue:todayStr(), due:"", notes:"", terms:""};
      state.items=[];
      $("#cus_name").value=""; $("#cus_email").value=""; $("#cus_phone").value=""; $("#cus_address").value=""; $("#cus_vat").value="";
      $("#doc_issue").value=todayStr(); $("#doc_due").value=addDays($("#doc_issue").value,14);
      $("#doc_number").value = nextNumberPlain(); state.doc.number = $("#doc_number").value;
      renderItems(); calc(); setTab("editor");
    });

    function escapeHtml(s){ return (s||"").replace(/[&<>"']/g,m=>({"&":"&amp;","<":"&lt;","&gt;":"&gt;",'"':"&quot;","'":"&#039;"}[m])); }

    function renderPreview(){
      const isQuote=(state.doc.type==="Quote");
      const T=state._totals||{subtotal:0,tax:0,total:0};
      const dueLabel=isQuote?"Valid Until":"Due Date";

      // Logo or business name (if provided)
      const logoHTML = state.business.logo
        ? `<img src="${state.business.logo}" style="height:56px"/>`
        : (state.business.name ? `<div style="font-weight:800;font-size:1.2rem">${escapeHtml(state.business.name)}</div>` : ``);

      // Business lines only if there is content (prevents "• •")
      const line1Parts = [];
      if(state.business.address) line1Parts.push(escapeHtml(state.business.address));
      if(state.business.email)   line1Parts.push(escapeHtml(state.business.email));
      if(state.business.phone)   line1Parts.push(escapeHtml(state.business.phone));
      const bizLine1 = line1Parts.length ? `<div class="muted small">${line1Parts.join(" • ")}</div>` : ``;

      const line2Parts = [];
      if(state.business.vat)  line2Parts.push(`VAT/Company: ${escapeHtml(state.business.vat)}`);
      if(state.business.iban) line2Parts.push(`IBAN: ${escapeHtml(state.business.iban)}`);
      const bizLine2 = line2Parts.length ? `<div class="muted small">${line2Parts.join(" • ")}</div>` : ``;

      $("#doc_preview").innerHTML = `
        <div class="head">
          <div>
            ${logoHTML || `<div class="muted small"> </div>`}
            ${bizLine1}
            ${bizLine2}
          </div>
          <div style="text-align:right">
            <div class="doc-title">${isQuote?"QUOTE":"INVOICE"}</div>
            <div class="doc-num">${escapeHtml(state.doc.number||"")}</div>
            <div><span class="small">${escapeHtml(state.doc.currency||"€")}</span></div>
          </div>
        </div>

        <div class="grid-2" style="padding:0 20px">
          <div class="boxed">
            <h3>Bill To</h3>
            <div>${escapeHtml(state.customer.name||"")}</div>
            <div class="muted small">${escapeHtml(state.customer.address||"")}</div>
            <div class="muted small">${escapeHtml(state.customer.email||"")} ${state.customer.phone?(" • "+escapeHtml(state.customer.phone)):""}</div>
            ${state.customer.vat ? `<div class="muted small">VAT: <strong>${escapeHtml(state.customer.vat)}</strong></div>` : ``}
          </div>
          <div class="boxed">
            <h3>Details</h3>
            <div class="small">Issue Date: <strong>${escapeHtml(state.doc.issue||"")}</strong></div>
            <div class="small">${dueLabel}: <strong>${escapeHtml(state.doc.due||"")}</strong></div>
            <div class="small">Tax: <strong>${$("#apply_tax").checked ? (parseNum($("#tax_rate").value).toFixed(2)+"%") : "No tax"}</strong></div>
          </div>
        </div>

        <div style="padding:0 20px 14px">
          <table>
            <thead>
              <tr><th>Description</th><th class="right">Qty</th><th class="right">Unit Price</th><th class="right">Line Total</th></tr>
            </thead>
            <tbody>
              ${state.items.map(it=>`
                <tr>
                  <td>${escapeHtml(it.desc||"")}</td>
                  <td class="right">${(parseNum(it.qty)).toFixed(2)}</td>
                  <td class="right">${money(parseNum(it.price))}</td>
                  <td class="right">${money(parseNum(it.qty)*parseNum(it.price))}</td>
                </tr>
              `).join("")}
              ${state.items.length===0?`<tr><td colspan="4" class="muted center small">No items yet</td></tr>`:""}
            </tbody>
            <tfoot>
              <tr><td colspan="3" class="right">Subtotal</td><td class="right">${money(T.subtotal)}</td></tr>
              <tr><td colspan="3" class="right">Tax</td><td class="right">${money(T.tax)}</td></tr>
              <tr><td colspan="3" class="right"><strong>Total</strong></td><td class="right"><strong>${money(T.total)}</strong></td></tr>
            </tfoot>
          </table>
        </div>

        <div class="footer">
          <div style="flex:1">
            <h3>Notes</h3>
            <div class="small">${escapeHtml(state.doc.notes||"")}</div>
          </div>
          <div style="flex:1">
            <h3>Terms</h3>
            <div class="small">${escapeHtml(state.doc.terms||"")}</div>
          </div>
        </div>`;
    }

    // -------- Dashboard --------
    function computeTotals(doc){
      const items = Array.isArray(doc.items) ? doc.items : [];
      const applyTax = doc.settings && (doc.settings.applyTax!==undefined ? doc.settings.applyTax : true);
      const rate = (doc.settings && doc.settings.taxRate!=null) ? parseFloat(doc.settings.taxRate)||0 : 0;
      const subtotal = items.reduce((s,it)=> s + ((parseFloat(it.qty)||0)*(parseFloat(it.price)||0)), 0);
      const tax = applyTax ? subtotal * (rate/100) : 0;
      const total = subtotal + tax;
      return {subtotal,tax,total};
    }
    function listSavedNumbers(){
      const idxKey="doc_index";
      const index = JSON.parse(localStorage.getItem(idxKey) || "[]");
      for(const k in localStorage){
        if(k && k.startsWith && k.startsWith("doc::")){
          const num = k.slice(5);
          if(!index.includes(num)) index.push(num);
        }
      }
      return index;
    }
    function buildDashboard(){
      const typeFilter = $("#dash_type").value;
      const q = ($("#dash_search").value || "").toLowerCase().trim();
      const curSym = $("#dash_currency").value || "€";
      const nums = listSavedNumbers();
      const rows = [];
      let sum = {subtotal:0,tax:0,total:0};

      nums.forEach(num=>{
        try{
          const raw = localStorage.getItem("doc::"+num);
          if(!raw) return;
          const doc = JSON.parse(raw);
          const t = computeTotals(doc);
          const d = doc.doc || {};
          const customer = (doc.customer && (doc.customer.name||"")) || "";
          if(typeFilter!=="ALL" && d.type!==typeFilter) return;
          if(q && !(String(d.number||"").toLowerCase().includes(q) || String(customer).toLowerCase().includes(q))) return;
          rows.push({ num: d.number||num, type: d.type||"", issue: d.issue||"", due: d.due||"", customer, ...t });
          sum.subtotal += t.subtotal; sum.tax += t.tax; sum.total += t.total;
        }catch(e){ /* ignore */ }
      });

      const tb = $("#dash_body");
      tb.innerHTML = rows.map((r,i)=>`
        <tr>
          <td class="center"><input type="checkbox" data-sel="${i}" data-num="${r.num}"/></td>
          <td>${r.num}</td>
          <td>${r.type}</td>
          <td>${r.issue||""}</td>
          <td>${r.due||""}</td>
          <td>${(r.customer||"").replace(/[&<>"']/g, m=>({"&":"&amp;","<":"&lt;","&gt;":"&gt;",'"':"&quot;","'":"&#039;"}[m]))}</td>
          <td class="right">${money(r.subtotal, curSym)}</td>
          <td class="right">${money(r.tax, curSym)}</td>
          <td class="right">${money(r.total, curSym)}</td>
          <td class="right">
            <button data-open="${r.num}">Edit</button>
            <button data-remove="${r.num}" class="danger">Delete</button>
          </td>
        </tr>
      `).join("");

      $("#sum_subtotal").textContent = money(sum.subtotal, curSym);
      $("#sum_tax").textContent = money(sum.tax, curSym);
      $("#sum_total").textContent = money(sum.total, curSym);

      const selAll = $("#dash_select_all");
      const openBtn = $("#dash_open_selected");
      const delSelBtn = $("#dash_delete_selected");
      function updateBtns(){
        const any = tb.querySelector('input[type="checkbox"][data-sel]:checked');
        openBtn.disabled = !any; delSelBtn.disabled = !any;
      }
      selAll.checked = false;
      selAll.onchange = ()=>{
        tb.querySelectorAll('input[type="checkbox"][data-sel]').forEach(ch=> ch.checked = selAll.checked);
        updateBtns();
      };
      tb.querySelectorAll('input[type="checkbox"][data-sel]').forEach(ch=> ch.addEventListener("change", updateBtns));
      updateBtns();

      tb.onclick = (e)=>{
        const open = e.target.closest("button[data-open]");
        const rem = e.target.closest("button[data-remove]");
        if(open){
          const num=open.dataset.open;
          const raw=localStorage.getItem("doc::"+num);
          if(!raw){ alert("Not found: "+num); return; }
          fromJSON(raw); setTab("editor");
        }
        if(rem){
          const num=rem.dataset.remove;
          if(confirm("Delete document "+num+" ?")){
            deleteDoc(num);
            buildDashboard();
          }
        }
      };

      openBtn.onclick = ()=>{
        const ch = tb.querySelector('input[type="checkbox"][data-sel]:checked');
        if(!ch) return;
        const num = ch.getAttribute("data-num");
        const raw = localStorage.getItem("doc::"+num);
        if(!raw){ alert("Not found: "+num); return; }
        fromJSON(raw); setTab("editor");
      };
      delSelBtn.onclick = ()=>{
        const selected = Array.from(tb.querySelectorAll('input[type="checkbox"][data-sel]:checked'))
          .map(ch=>ch.getAttribute("data-num"));
        if(selected.length===0) return;
        if(!confirm("Delete "+selected.length+" document(s)?")) return;
        selected.forEach(num=> deleteDoc(num));
        buildDashboard();
      };
    }

    $("#dash_type").addEventListener("change", buildDashboard);
    $("#dash_search").addEventListener("input", buildDashboard);
    $("#dash_currency").addEventListener("input", buildDashboard);
    $("#dash_refresh").addEventListener("click", buildDashboard);

    // Tabs + init
    function setTab(tab){
      if(tab==="dashboard"){
        $("#dashboardPanel").classList.remove("hidden");
        $("#editorPanel").classList.add("hidden");
        $("#tabDashboard").classList.add("active");
        $("#tabEditor").classList.remove("active");
        buildDashboard();
      }else{
        $("#dashboardPanel").classList.add("hidden");
        $("#editorPanel").classList.remove("hidden");
        $("#tabEditor").classList.add("active");
        $("#tabDashboard").classList.remove("active");
      }
    }
    $("#tabEditor").addEventListener("click", ()=> setTab("editor"));
    $("#tabDashboard").addEventListener("click", ()=> setTab("dashboard"));

    function init(){
      $("#doc_issue").value=todayStr();
      $("#doc_due").value=addDays($("#doc_issue").value,14);
      state.doc.issue=$("#doc_issue").value; state.doc.due=$("#doc_due").value; state.doc.type=$("#doc_type").value;
      $("#doc_number").value = nextNumberPlain(); state.doc.number = $("#doc_number").value;
      addItem("",1,0);
      $("#doc_terms").value="Payment due within 14 days. Late fees may apply."; state.doc.terms=$("#doc_terms").value;
      renderItems(); renderPreview(); calc(); setTab("editor");
    }
    init();
  })();
  </script>
</body>
</html>
